# FaceTrace Security Implementation

## Overview

This document outlines the comprehensive security measures implemented for the FaceTrace application, specifically focusing on protecting the "Run FaceTrace" functionality from automated abuse and ensuring legitimate usage.

## Security Measures Implemented

### 1. CAPTCHA Verification

#### Frontend Integration
- **Component**: `src/components/CaptchaVerification.tsx`
- **Hook**: `useCaptcha()` for state management
- **Integration**: Added to search page (`src/app/search/page.tsx`)

#### Features
- Google reCAPTCHA v2 integration
- Responsive design with light/dark theme support
- Error handling and user feedback
- Automatic reset functionality
- Conditional display (only when auth is enabled and files are selected)

#### Configuration
```env
NEXT_PUBLIC_RECAPTCHA_SITE_KEY="6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI"
RECAPTCHA_SECRET_KEY="6LeIxAcTAAAAAGG-vFI1TnRWxMZNFuojJ4WifJWe"
```

### 2. Server-Side CAPTCHA Verification

#### Implementation
- **File**: `src/lib/captcha.ts`
- **Function**: `verifyCaptcha(token, remoteip)`
- **Integration**: Added to `/api/start-search` endpoint

#### Features
- Server-side token verification with Google's API
- IP address extraction and validation
- Configurable score thresholds for reCAPTCHA v3
- Comprehensive error handling
- Timeout protection (10-second limit)

### 3. Rate Limiting

#### Middleware Enhancement
- **File**: `src/middleware.ts`
- **Function**: `securityMiddleware()`

#### Rate Limits
- **Search Initiation** (`/api/start-search`): 5 requests per 15 minutes
- **Search Operations** (`/api/search`): 10 requests per 15 minutes  
- **Contact Form** (`/api/contact`): 3 requests per 1 hour

#### Features
- IP-based rate limiting
- Different limits for different endpoints
- Proper HTTP 429 responses with retry headers
- In-memory storage with automatic cleanup

### 4. User Agent Filtering

#### Bot Detection
- Blocks suspicious user agents on sensitive endpoints
- Patterns detected:
  - `/bot/i`, `/crawler/i`, `/spider/i`
  - `/scraper/i`, `/curl/i`, `/wget/i`
  - `/python/i`, `/requests/i`

#### Implementation
- Only applies to `/api/start-search` endpoint
- Returns HTTP 403 for blocked requests
- Allows legitimate crawlers on public pages

### 5. Enhanced Security Headers

#### Existing Headers (vercel.json)
- `X-Content-Type-Options: nosniff`
- `X-Frame-Options: DENY`
- `X-XSS-Protection: 1; mode=block`
- `Strict-Transport-Security: max-age=31536000; includeSubDomains; preload`
- `Referrer-Policy: strict-origin-when-cross-origin`
- `Permissions-Policy: camera=(), microphone=(), geolocation=()`

### 6. Input Validation

#### API Endpoint Protection
- **File**: `src/app/api/start-search/route.ts`
- Validates all required parameters
- Email format validation for guest users
- CAPTCHA token validation
- Proper error responses for invalid inputs

## Testing Mode Compatibility

### Development/Testing Override
- CAPTCHA verification is skipped when `DISABLE_AUTH=true`
- Rate limiting still applies but with logging
- Maintains security infrastructure for easy production deployment

### Environment Variables
```env
DISABLE_AUTH=true          # Disables CAPTCHA requirement
DISABLE_PAYMENT=true       # Disables payment requirements
```

## API Integration

### Updated API Service
- **File**: `src/app/services/apiService.ts`
- **Function**: `startSearch()` now accepts `captchaToken` parameter
- Passes CAPTCHA token to backend for verification

### Frontend Integration
- CAPTCHA component appears after file upload
- Button disabled until CAPTCHA is completed
- Token automatically passed to API calls
- Reset functionality for new searches

## Security Flow

### Complete Request Flow
1. **User uploads files** → CAPTCHA component appears
2. **User completes CAPTCHA** → Token generated and stored
3. **User clicks "Run FaceTrace"** → Validation checks:
   - Files uploaded ✓
   - Email valid (for guests) ✓
   - CAPTCHA completed ✓
4. **Middleware checks**:
   - Rate limiting ✓
   - User agent validation ✓
5. **API endpoint validation**:
   - Input validation ✓
   - CAPTCHA verification ✓
   - IP address logging ✓
6. **Search proceeds** if all checks pass

## Error Handling

### User-Friendly Messages
- "Please complete the security verification (CAPTCHA) before proceeding."
- "Too many requests. Please try again later."
- "CAPTCHA verification failed. Please try again."

### Logging
- All security events are logged with timestamps
- IP addresses tracked for rate limiting
- CAPTCHA verification results logged
- Failed attempts logged for monitoring

## Dependencies Added

### NPM Packages
```json
{
  "react-google-recaptcha": "^3.1.0",
  "@types/react-google-recaptcha": "^2.1.9"
}
```

## Files Modified/Created

### New Files
- `src/components/CaptchaVerification.tsx` - CAPTCHA component
- `src/lib/captcha.ts` - Server-side verification utilities
- `src/test/captcha-integration.test.ts` - Test suite
- `SECURITY_IMPLEMENTATION.md` - This documentation

### Modified Files
- `package.json` - Added CAPTCHA dependencies
- `.env` - Added reCAPTCHA configuration
- `src/middleware.ts` - Enhanced with rate limiting and security
- `src/app/api/start-search/route.ts` - Added CAPTCHA verification
- `src/app/services/apiService.ts` - Updated to pass CAPTCHA token
- `src/app/search/page.tsx` - Integrated CAPTCHA component

## Production Deployment

### Required Steps
1. **Get Production reCAPTCHA Keys**:
   - Register domain at https://www.google.com/recaptcha/admin
   - Replace test keys with production keys

2. **Environment Variables**:
   ```env
   NEXT_PUBLIC_RECAPTCHA_SITE_KEY="your_production_site_key"
   RECAPTCHA_SECRET_KEY="your_production_secret_key"
   DISABLE_AUTH=false  # Enable CAPTCHA in production
   ```

3. **Rate Limiting Considerations**:
   - Consider Redis for distributed rate limiting
   - Monitor rate limit effectiveness
   - Adjust limits based on usage patterns

## Monitoring and Maintenance

### Recommended Monitoring
- Track CAPTCHA verification success/failure rates
- Monitor rate limiting effectiveness
- Log suspicious activity patterns
- Track API response times

### Maintenance Tasks
- Regularly update reCAPTCHA keys
- Review and adjust rate limits
- Update bot detection patterns
- Monitor for new attack vectors

## Law Enforcement Considerations

### Specialized Features
- Enhanced logging for audit trails
- IP address tracking for investigations
- Rate limiting prevents resource abuse
- CAPTCHA ensures human verification
- Maintains evidence integrity through security measures

### Compliance
- Security measures support legitimate law enforcement usage
- Prevents automated abuse that could compromise investigations
- Maintains system availability for critical operations
- Provides audit trail for accountability

## Conclusion

The implemented security measures provide comprehensive protection against automated abuse while maintaining usability for legitimate users. The system is designed to be robust, scalable, and suitable for law enforcement use cases while preventing misuse by bad actors.
