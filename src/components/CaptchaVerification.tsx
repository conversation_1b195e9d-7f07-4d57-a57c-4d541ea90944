'use client';

import React, { useRef, useCallback, useState, useEffect } from 'react';
import ReCA<PERSON><PERSON><PERSON> from 'react-google-recaptcha';
import { motion, AnimatePresence } from 'framer-motion';

interface CaptchaVerificationProps {
  onVerify: (token: string | null) => void;
  onError?: (error: string) => void;
  disabled?: boolean;
  theme?: 'light' | 'dark';
  size?: 'compact' | 'normal';
  className?: string;
}

export const CaptchaVerification: React.FC<CaptchaVerificationProps> = ({
  onVerify,
  onError,
  disabled = false,
  theme = 'light',
  size = 'normal',
  className = ''
}) => {
  const recaptchaRef = useRef<ReCAPTCHA>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isVerified, setIsVerified] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const siteKey = process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY;

  useEffect(() => {
    if (!siteKey) {
      const errorMsg = 'reCAPTCHA site key not configured';
      setError(errorMsg);
      onError?.(errorMsg);
    }
  }, [siteKey, onError]);

  const handleChange = useCallback((token: string | null) => {
    setIsLoading(false);
    setError(null);
    
    if (token) {
      setIsVerified(true);
      console.log('[captcha] Verification successful');
    } else {
      setIsVerified(false);
      console.log('[captcha] Verification cleared');
    }
    
    onVerify(token);
  }, [onVerify]);

  const handleExpired = useCallback(() => {
    setIsVerified(false);
    setError('CAPTCHA expired. Please verify again.');
    onVerify(null);
    console.log('[captcha] CAPTCHA expired');
  }, [onVerify]);

  const handleError = useCallback(() => {
    setIsLoading(false);
    setIsVerified(false);
    const errorMsg = 'CAPTCHA verification failed. Please try again.';
    setError(errorMsg);
    onError?.(errorMsg);
    onVerify(null);
    console.error('[captcha] CAPTCHA error occurred');
  }, [onVerify, onError]);

  const handleLoad = useCallback(() => {
    setIsLoading(false);
    console.log('[captcha] CAPTCHA loaded');
  }, []);

  const reset = useCallback(() => {
    if (recaptchaRef.current) {
      recaptchaRef.current.reset();
      setIsVerified(false);
      setError(null);
      onVerify(null);
    }
  }, [onVerify]);

  // Expose reset method to parent components
  React.useImperativeHandle(recaptchaRef, () => ({
    reset
  }));

  if (!siteKey) {
    return (
      <div className={`p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg ${className}`}>
        <div className="flex items-center">
          <svg className="w-5 h-5 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
          <span className="text-red-700 dark:text-red-300 text-sm font-medium">
            CAPTCHA configuration error
          </span>
        </div>
      </div>
    );
  }

  return (
    <div className={`captcha-container ${className}`}>
      <div className="flex flex-col items-center space-y-3">
        {/* CAPTCHA Widget */}
        <div className="relative">
          <AnimatePresence>
            {isLoading && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="absolute inset-0 bg-white/80 dark:bg-gray-800/80 flex items-center justify-center rounded-lg z-10"
              >
                <div className="flex items-center space-x-2">
                  <svg className="animate-spin h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span className="text-sm text-gray-600 dark:text-gray-300">Loading...</span>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          <ReCAPTCHA
            ref={recaptchaRef}
            sitekey={siteKey}
            onChange={handleChange}
            onExpired={handleExpired}
            onError={handleError}
            onLoad={handleLoad}
            theme={theme}
            size={size}
            disabled={disabled}
          />
        </div>

        {/* Status Indicator */}
        <AnimatePresence>
          {isVerified && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="flex items-center space-x-2 text-green-600 dark:text-green-400"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="text-sm font-medium">Verified</span>
            </motion.div>
          )}

          {error && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="flex items-center space-x-2 text-red-600 dark:text-red-400"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
              <span className="text-sm font-medium">{error}</span>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Security Notice */}
        <div className="text-center">
          <p className="text-xs text-gray-500 dark:text-gray-400 max-w-sm">
            This security verification helps protect against automated abuse and ensures legitimate usage of FaceTrace services.
          </p>
        </div>
      </div>
    </div>
  );
};

export default CaptchaVerification;

// Hook for easier usage
export const useCaptcha = () => {
  const [captchaToken, setCaptchaToken] = useState<string | null>(null);
  const [isVerified, setIsVerified] = useState(false);

  const handleCaptchaVerify = useCallback((token: string | null) => {
    setCaptchaToken(token);
    setIsVerified(!!token);
  }, []);

  const resetCaptcha = useCallback(() => {
    setCaptchaToken(null);
    setIsVerified(false);
  }, []);

  return {
    captchaToken,
    isVerified,
    handleCaptchaVerify,
    resetCaptcha
  };
};
