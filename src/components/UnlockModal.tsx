'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Elements, PaymentElement, useStripe, useElements } from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';
import { useAuth, useUser } from '@clerk/nextjs';
// import { SignIn } from '@clerk/nextjs'; // Hidden - auth disabled for free access
import { Button } from '@/components/ui/button';
import PRICING_CONFIG from '@/config/pricing';

// Check if authentication is disabled
const isAuthDisabled = process.env.NEXT_PUBLIC_DISABLE_AUTH === 'true';

// Simplified modal steps - maximum 3 steps
type ModalStep = 'selection' | 'payment' | 'success';

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY || '');

// Simplified payment form component
const PaymentForm = ({
  onSuccess,
  onError,
  email,
}: {
  onSuccess: (paymentIntentId: string) => void;
  onError: (message: string) => void;
  email: string;
}) => {
  const stripe = useStripe();
  const elements = useElements();
  const [isProcessing, setIsProcessing] = useState(false);
  const [agreedToTerms, setAgreedToTerms] = useState(false);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements) {
      onError('Stripe has not loaded yet. Please try again.');
      return;
    }

    if (!agreedToTerms) {
      onError('Please agree to the terms and conditions');
      return;
    }

    setIsProcessing(true);

    try {
      const { error, paymentIntent } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: window.location.origin,
          receipt_email: email,
        },
        redirect: 'if_required',
      });

      if (error) {
        throw new Error(error.message || 'Something went wrong with your payment');
      }

      if (paymentIntent && paymentIntent.status === 'succeeded') {
        onSuccess(paymentIntent.id);
      } else {
        throw new Error('Payment status unknown. Please contact support.');
      }
    } catch (err: unknown) {
      const error = err as Error;
      onError(error.message || 'Payment processing failed. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <PaymentElement />

      <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Email</span>
        </div>
        <div className="text-gray-800 dark:text-gray-200 font-medium">
          {email}
        </div>
      </div>

      <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Payment Amount</span>
        </div>
        <div className="text-gray-800 dark:text-gray-200 font-bold">
          {PRICING_CONFIG.getFormattedPrice()}
        </div>
        <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
          One-time payment, no subscription
        </div>
      </div>

      <div className="flex items-start space-x-2">
        <input
          id="terms"
          name="terms"
          type="checkbox"
          checked={agreedToTerms}
          onChange={(e) => setAgreedToTerms(e.target.checked)}
          className="mt-1 h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
        />
        <label htmlFor="terms" className="text-xs text-gray-500 dark:text-gray-400">
          I agree to the <a href="/terms-of-use" target="_blank" className="text-blue-600 dark:text-blue-400 hover:underline">Terms of Service</a> and <a href="/privacy-policy" target="_blank" className="text-blue-600 dark:text-blue-400 hover:underline">Privacy Policy</a>.
        </label>
      </div>

      <Button
        type="submit"
        disabled={isProcessing || !agreedToTerms}
        className="w-full"
      >
        {isProcessing ? (
          <span className="flex items-center justify-center">
            <svg className="animate-spin h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 718-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Processing Payment...
          </span>
        ) : (
          `Pay ${PRICING_CONFIG.getFormattedPrice()} to Unlock Results`
        )}
      </Button>

      <div className="text-xs text-center text-gray-500 dark:text-gray-400 mt-4">
        By proceeding, you agree to our Terms and Privacy Policy.
        Your payment is processed securely through Stripe.
      </div>
    </form>
  );
};

// Success screen component
const SuccessScreen = ({
  onClose,
  message = "Your results are now available!",
}: {
  onClose: () => void;
  message?: string;
}) => {
  const [countdown, setCountdown] = useState(3);

  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          onClose();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [onClose]);

  return (
    <div className="text-center p-6">
      <div className="w-20 h-20 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-6">
        <svg
          className="w-10 h-10 text-green-500"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M5 13l4 4L19 7"
          ></path>
        </svg>
      </div>
      <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
        Success!
      </h3>
      <p className="text-gray-600 dark:text-gray-300 mb-4">
        {message}
      </p>
      <p className="text-sm text-gray-500 dark:text-gray-400">
        Closing automatically in {countdown} seconds
      </p>
    </div>
  );
};

// Component that uses Clerk hooks (only when auth is enabled)
function UnlockModalWithClerk({
  isOpen,
  onClose,
  reportId,
  reportIdentifier,
}: {
  isOpen: boolean;
  onClose: (success: boolean) => void;
  reportId: number;
  reportIdentifier: string;
}) {
  const { isLoaded: isAuthLoaded, userId } = useAuth();
  const { user } = useUser();
  
  // Simplified state management
  const [currentStep, setCurrentStep] = useState<ModalStep>('selection');
  const [userTokens, setUserTokens] = useState<number>(0);
  const [guestEmail, setGuestEmail] = useState<string>('');
  const [clientSecret, setClientSecret] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // Fetch user tokens if authenticated
  useEffect(() => {
    if (isOpen && isAuthLoaded && userId) {
      fetchUserTokens();
    }
  }, [isOpen, isAuthLoaded, userId]);

  const fetchUserTokens = async () => {
    try {
      const response = await fetch('/api/data?action=user-data');
      if (response.ok) {
        const data = await response.json();
        setUserTokens(data.tokens || 0);
      }
    } catch (error) {
      console.error('Error fetching tokens:', error);
      setUserTokens(0);
    }
  };

  // Create payment intent
  const createPaymentIntent = async (email: string) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/core?action=create-payment-intent', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email,
          reportId: reportIdentifier || String(reportId),
          serviceType: userId ? 'report_unlock_auth' : 'report_unlock_guest',
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to create payment intent');
      }
      
      const data = await response.json();
      setClientSecret(data.clientSecret);
      setCurrentStep('payment');
    } catch (error: any) {
      setError(error.message || 'Failed to create payment intent');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle token unlock
  const handleTokenUnlock = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/reports/unlock-with-token', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          reportId: reportIdentifier || String(reportId),
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to unlock with token');
      }
      
      setUserTokens(prev => Math.max(0, prev - 1));
      setCurrentStep('success');
      setTimeout(() => onClose(true), 2000);
    } catch (error: any) {
      setError(error.message || 'Failed to unlock with token');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle free unlock (bypassing payment)
  const handleFreeUnlock = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/reports/unlock-free', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          reportId: reportIdentifier || String(reportId),
          email: guestEmail || user?.primaryEmailAddress?.emailAddress,
        }),
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to unlock report');
      }

      setCurrentStep('success');
      setTimeout(() => onClose(true), 2000);
    } catch (error: any) {
      setError(error.message || 'Failed to unlock report');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle payment success (kept for future use)
  const handlePaymentSuccess = async (paymentIntentId: string) => {
    setIsLoading(true);

    try {
      const response = await fetch('/api/reports/unlock', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          reportId: reportIdentifier || String(reportId),
          paymentIntentId,
          email: guestEmail || user?.primaryEmailAddress?.emailAddress,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to unlock with payment');
      }

      setCurrentStep('success');
      setTimeout(() => onClose(true), 2000);
    } catch (error: any) {
      setError(error.message || 'Failed to unlock with payment');
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <motion.div
        className="bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-md w-full overflow-hidden relative"
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
      >
        {/* Close button */}
        <button
          onClick={() => onClose(false)}
          className="absolute top-3 right-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 z-10"
          aria-label="Close"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>

        {/* Modal content */}
        <div className="p-6">
          {/* Modal header */}
          <div className="text-center mb-6">
            <div className="h-12 w-12 mx-auto mb-4 rounded-full flex items-center justify-center bg-gradient-to-r from-[#92A5FF] to-[#FFA1BF]">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
            </div>
            <h2 className="text-xl font-bold text-gray-900 dark:text-white">Unlock Full Results</h2>
            <p className="text-gray-600 dark:text-gray-400 text-sm">
              {currentStep === 'selection' ? 'Choose how to unlock' :
               currentStep === 'payment' ? 'Complete payment to unlock' :
               'Report unlocked successfully!'}
            </p>
          </div>

          {/* Error message */}
          {error && (
            <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg text-red-600 dark:text-red-400 text-sm">
              <div className="flex items-center">
                <svg className="w-5 h-5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                <span>{error}</span>
              </div>
            </div>
          )}

          {/* Step content */}
          {currentStep === 'selection' && (
            <div className="space-y-4">
              <div className="text-center mb-4">
                <div className="flex justify-center gap-2 text-2xl mb-2">
                  🔍 👤 🌐
                </div>
                <p className="text-gray-700 dark:text-gray-300">
                  View complete source details and unlock full access to this report
                </p>
              </div>

              <div className="space-y-3">
                {/* Show token option for authenticated users with tokens */}
                {userId && userTokens > 0 && (
                  <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <div className="flex justify-between items-center mb-3">
                      <span className="text-blue-800 dark:text-blue-300 font-medium">Your Tokens</span>
                      <span className="bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-300 px-3 py-1 rounded-full font-bold">
                        {userTokens} {userTokens === 1 ? 'Token' : 'Tokens'}
                      </span>
                    </div>
                    <Button
                      onClick={handleTokenUnlock}
                      disabled={isLoading}
                      className="w-full bg-gradient-to-r from-[#92A5FF] to-[#FFA1BF] text-white"
                    >
                      {isLoading ? 'Unlocking...' : 'Use 1 Token to Unlock'}
                    </Button>
                  </div>
                )}

                {/* Free unlock option (payment disabled) */}
                <div className="space-y-3">
                  {userId && userTokens > 0 && (
                    <div className="flex items-center">
                      <div className="flex-1 h-px bg-gray-200 dark:bg-gray-700"></div>
                      <span className="px-3 text-gray-500 dark:text-gray-400 text-sm">OR</span>
                      <div className="flex-1 h-px bg-gray-200 dark:bg-gray-700"></div>
                    </div>
                  )}

                  <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border-2 border-green-200 dark:border-green-800">
                    <div className="text-center font-medium mb-3">
                      <div className="mb-2 text-green-800 dark:text-green-300">
                        Free Access
                      </div>
                      <div className="text-2xl text-transparent bg-clip-text bg-gradient-to-r from-green-600 to-green-500">
                        FREE
                      </div>
                    </div>
                    <p className="text-sm text-green-700 dark:text-green-400 mb-3 text-center">
                      Payment is currently disabled. Unlock your results for free!
                    </p>
                    <Button
                      onClick={handleFreeUnlock}
                      disabled={isLoading}
                      className="w-full bg-green-600 hover:bg-green-700 text-white"
                    >
                      {isLoading ? 'Unlocking...' : 'Unlock Results for Free'}
                    </Button>
                  </div>

                  {/* Payment option (hidden for now) */}
                  <div className="hidden p-4 bg-gray-50 dark:bg-gray-900/20 rounded-lg opacity-50">
                    <div className="text-center font-medium mb-3">
                      <div className="mb-2 text-gray-700 dark:text-gray-300">
                        One-Time Payment
                      </div>
                      <div className="text-2xl text-transparent bg-clip-text bg-gradient-to-r from-[#92A5FF] to-[#FFA1BF]">
                        {PRICING_CONFIG.getFormattedPrice()}
                      </div>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 text-center">
                      Payment temporarily disabled
                    </p>
                    <Button
                      disabled={true}
                      className="w-full bg-gray-400 text-white cursor-not-allowed"
                    >
                      Payment Disabled
                    </Button>
                  </div>
                </div>

                {/* Sign in option for guests (hidden since free unlock is available) */}
                {/* Authentication UI completely hidden for free access */}
              </div>
            </div>
          )}

          {currentStep === 'payment' && clientSecret && (
            <Elements stripe={stripePromise} options={{ clientSecret }}>
              <PaymentForm
                email={guestEmail || user?.primaryEmailAddress?.emailAddress || ''}
                onSuccess={handlePaymentSuccess}
                onError={(message) => setError(message)}
              />
            </Elements>
          )}

          {currentStep === 'success' && (
            <SuccessScreen
              onClose={() => onClose(true)}
              message="Your report has been unlocked successfully!"
            />
          )}

          {/* Security info at bottom */}
          <div className="flex items-center justify-center mt-4 text-xs text-gray-500 dark:text-gray-400">
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Free access - no payment required
          </div>
        </div>
      </motion.div>
    </div>
  );
}

// Component that doesn't use Clerk hooks (when auth is disabled)
function UnlockModalWithoutClerk({
  isOpen,
  onClose,
  reportId,
  reportIdentifier,
}: {
  isOpen: boolean;
  onClose: (success: boolean) => void;
  reportId: number;
  reportIdentifier: string;
}) {
  // Mock auth state when auth is disabled
  const isAuthLoaded = true;
  const userId = null;

  // Simplified state management
  const [currentStep, setCurrentStep] = useState<ModalStep>('selection');
  const [userTokens, setUserTokens] = useState<number>(0);
  const [guestEmail, setGuestEmail] = useState<string>('');
  const [clientSecret, setClientSecret] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // Fetch user tokens if authenticated (disabled when auth is off)
  useEffect(() => {
    if (isOpen && isAuthLoaded && userId) {
      fetchUserTokens();
    }
  }, [isOpen, isAuthLoaded, userId]);

  const fetchUserTokens = async () => {
    try {
      const response = await fetch('/api/data?action=user-data');
      if (response.ok) {
        const data = await response.json();
        setUserTokens(data.tokens || 0);
      }
    } catch (error) {
      console.error('Error fetching tokens:', error);
      setUserTokens(0);
    }
  };

  // Create payment intent
  const createPaymentIntent = async (email: string) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/core?action=create-payment-intent', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email,
          reportId: reportIdentifier || String(reportId),
          serviceType: userId ? 'report_unlock_auth' : 'report_unlock_guest',
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to create payment intent');
      }

      const data = await response.json();
      setClientSecret(data.clientSecret);
      setCurrentStep('payment');
    } catch (error: any) {
      setError(error.message || 'Failed to create payment intent');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle token unlock
  const handleTokenUnlock = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/reports/unlock-with-token', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          reportId: reportIdentifier || String(reportId),
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to unlock with token');
      }

      setUserTokens(prev => Math.max(0, prev - 1));
      setCurrentStep('success');
      setTimeout(() => onClose(true), 2000);
    } catch (error: any) {
      setError(error.message || 'Failed to unlock with token');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle free unlock (bypassing payment)
  const handleFreeUnlock = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/reports/unlock-free', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          reportId: reportIdentifier || String(reportId),
          email: guestEmail || '',
        }),
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to unlock report');
      }

      setCurrentStep('success');
      setTimeout(() => onClose(true), 2000);
    } catch (error: any) {
      setError(error.message || 'Failed to unlock report');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle payment success (kept for future use)
  const handlePaymentSuccess = async (paymentIntentId: string) => {
    setIsLoading(true);

    try {
      const response = await fetch('/api/reports/unlock', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          reportId: reportIdentifier || String(reportId),
          paymentIntentId,
          email: guestEmail || '',
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to unlock with payment');
      }

      setCurrentStep('success');
      setTimeout(() => onClose(true), 2000);
    } catch (error: any) {
      setError(error.message || 'Failed to unlock with payment');
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <motion.div
        className="bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-md w-full overflow-hidden relative"
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
      >
        {/* Close button */}
        <button
          onClick={() => onClose(false)}
          className="absolute top-3 right-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 z-10"
          aria-label="Close"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>

        {/* Modal content */}
        <div className="p-6">
          {/* Modal header */}
          <div className="text-center mb-6">
            <div className="h-12 w-12 mx-auto mb-4 rounded-full flex items-center justify-center bg-gradient-to-r from-[#92A5FF] to-[#FFA1BF]">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
            </div>
            <h2 className="text-xl font-bold text-gray-900 dark:text-white">Unlock Full Results</h2>
            <p className="text-gray-600 dark:text-gray-400 text-sm">
              {currentStep === 'selection' ? 'Choose how to unlock' :
               currentStep === 'payment' ? 'Complete payment to unlock' :
               'Report unlocked successfully!'}
            </p>
          </div>

          {/* Error message */}
          {error && (
            <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg text-red-600 dark:text-red-400 text-sm">
              <div className="flex items-center">
                <svg className="w-5 h-5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                <span>{error}</span>
              </div>
            </div>
          )}

          {/* Step content */}
          {currentStep === 'selection' && (
            <div className="space-y-4">
              <div className="text-center mb-4">
                <div className="flex justify-center gap-2 text-2xl mb-2">
                  🔍 👤 🌐
                </div>
                <p className="text-gray-700 dark:text-gray-300">
                  View complete source details and unlock full access to this report
                </p>
              </div>

              <div className="space-y-3">
                {/* Show token option for authenticated users with tokens */}
                {userId && userTokens > 0 && (
                  <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <div className="flex justify-between items-center mb-3">
                      <span className="text-blue-800 dark:text-blue-300 font-medium">Your Tokens</span>
                      <span className="bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-300 px-3 py-1 rounded-full font-bold">
                        {userTokens} {userTokens === 1 ? 'Token' : 'Tokens'}
                      </span>
                    </div>
                    <Button
                      onClick={handleTokenUnlock}
                      disabled={isLoading}
                      className="w-full bg-gradient-to-r from-[#92A5FF] to-[#FFA1BF] text-white"
                    >
                      {isLoading ? 'Unlocking...' : 'Use 1 Token to Unlock'}
                    </Button>
                  </div>
                )}

                {/* Free unlock option (payment disabled) */}
                <div className="space-y-3">
                  {userId && userTokens > 0 && (
                    <div className="flex items-center">
                      <div className="flex-1 h-px bg-gray-200 dark:bg-gray-700"></div>
                      <span className="px-3 text-gray-500 dark:text-gray-400 text-sm">OR</span>
                      <div className="flex-1 h-px bg-gray-200 dark:bg-gray-700"></div>
                    </div>
                  )}

                  <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border-2 border-green-200 dark:border-green-800">
                    <div className="text-center font-medium mb-3">
                      <div className="mb-2 text-green-800 dark:text-green-300">
                        Free Access
                      </div>
                      <div className="text-2xl text-transparent bg-clip-text bg-gradient-to-r from-green-600 to-green-500">
                        FREE
                      </div>
                    </div>
                    <p className="text-sm text-green-700 dark:text-green-400 mb-3 text-center">
                      Payment is currently disabled. Unlock your results for free!
                    </p>
                    <Button
                      onClick={handleFreeUnlock}
                      disabled={isLoading}
                      className="w-full bg-green-600 hover:bg-green-700 text-white"
                    >
                      {isLoading ? 'Unlocking...' : 'Unlock Results for Free'}
                    </Button>
                  </div>

                  {/* Payment option (hidden for now) */}
                  <div className="hidden p-4 bg-gray-50 dark:bg-gray-900/20 rounded-lg opacity-50">
                    <div className="text-center font-medium mb-3">
                      <div className="mb-2 text-gray-700 dark:text-gray-300">
                        One-Time Payment
                      </div>
                      <div className="text-2xl text-transparent bg-clip-text bg-gradient-to-r from-[#92A5FF] to-[#FFA1BF]">
                        {PRICING_CONFIG.getFormattedPrice()}
                      </div>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 text-center">
                      Payment temporarily disabled
                    </p>
                    <Button
                      disabled={true}
                      className="w-full bg-gray-400 text-white cursor-not-allowed"
                    >
                      Payment Disabled
                    </Button>
                  </div>
                </div>

                {/* Sign in option for guests (hidden since free unlock is available) */}
                {/* Authentication UI completely hidden for free access */}
              </div>
            </div>
          )}

          {currentStep === 'payment' && clientSecret && (
            <Elements stripe={stripePromise} options={{ clientSecret }}>
              <PaymentForm
                email={guestEmail || ''}
                onSuccess={handlePaymentSuccess}
                onError={(message) => setError(message)}
              />
            </Elements>
          )}

          {currentStep === 'success' && (
            <SuccessScreen
              onClose={() => onClose(true)}
              message="Your report has been unlocked successfully!"
            />
          )}

          {/* Security info at bottom */}
          <div className="flex items-center justify-center mt-4 text-xs text-gray-500 dark:text-gray-400">
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Free access - no payment required
          </div>
        </div>
      </motion.div>
    </div>
  );
}

// Main export that conditionally renders based on auth status
export default function UnlockModal({
  isOpen,
  onClose,
  reportId,
  reportIdentifier,
}: {
  isOpen: boolean;
  onClose: (success: boolean) => void;
  reportId: number;
  reportIdentifier: string;
}) {
  // Conditionally render based on auth status
  if (isAuthDisabled) {
    return (
      <UnlockModalWithoutClerk
        isOpen={isOpen}
        onClose={onClose}
        reportId={reportId}
        reportIdentifier={reportIdentifier}
      />
    );
  }

  return (
    <UnlockModalWithClerk
      isOpen={isOpen}
      onClose={onClose}
      reportId={reportId}
      reportIdentifier={reportIdentifier}
    />
  );
}
