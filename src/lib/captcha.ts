/**
 * CAPTCHA verification utilities for FaceTrace security
 */

import axios from 'axios';

export interface CaptchaVerificationResult {
  success: boolean;
  error?: string;
  score?: number; // For reCAPTCHA v3
  action?: string; // For reCAPTCHA v3
}

/**
 * Verify reCA<PERSON><PERSON><PERSON> token on the server side
 * @param token - The reCAPTCHA token from the client
 * @param remoteip - Optional IP address of the user
 * @returns Promise<CaptchaVerificationResult>
 */
export async function verifyCaptcha(
  token: string,
  remoteip?: string
): Promise<CaptchaVerificationResult> {
  const secretKey = process.env.RECAPTCHA_SECRET_KEY;
  
  if (!secretKey) {
    console.error('[captcha] RECAPTCHA_SECRET_KEY not configured');
    return {
      success: false,
      error: 'CAPTCHA verification not configured'
    };
  }

  if (!token) {
    return {
      success: false,
      error: 'CAPTCHA token is required'
    };
  }

  try {
    const verificationUrl = 'https://www.google.com/recaptcha/api/siteverify';
    
    const params = new URLSearchParams({
      secret: secretKey,
      response: token,
      ...(remoteip && { remoteip })
    });

    console.log('[captcha] Verifying CAPTCHA token...');
    
    const response = await axios.post(verificationUrl, params, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      timeout: 10000, // 10 second timeout
    });

    const data = response.data;
    
    console.log('[captcha] Verification response:', {
      success: data.success,
      score: data.score,
      action: data.action,
      hostname: data.hostname,
      challenge_ts: data.challenge_ts
    });

    if (!data.success) {
      const errorCodes = data['error-codes'] || [];
      console.warn('[captcha] Verification failed:', errorCodes);
      
      return {
        success: false,
        error: `CAPTCHA verification failed: ${errorCodes.join(', ')}`
      };
    }

    // For reCAPTCHA v3, check the score (0.0 to 1.0, higher is better)
    if (data.score !== undefined) {
      const minScore = 0.5; // Configurable threshold
      if (data.score < minScore) {
        console.warn(`[captcha] Score too low: ${data.score} < ${minScore}`);
        return {
          success: false,
          error: 'CAPTCHA verification failed: suspicious activity detected'
        };
      }
    }

    return {
      success: true,
      score: data.score,
      action: data.action
    };

  } catch (error) {
    console.error('[captcha] Error verifying CAPTCHA:', error);
    
    if (axios.isAxiosError(error)) {
      if (error.code === 'ECONNABORTED') {
        return {
          success: false,
          error: 'CAPTCHA verification timeout'
        };
      }
      
      return {
        success: false,
        error: `CAPTCHA verification failed: ${error.message}`
      };
    }
    
    return {
      success: false,
      error: 'CAPTCHA verification failed: unknown error'
    };
  }
}

/**
 * Extract client IP address from request headers
 * @param request - Next.js request object
 * @returns string | undefined
 */
export function getClientIP(request: Request): string | undefined {
  // Check various headers for the real IP
  const headers = request.headers;
  
  const forwardedFor = headers.get('x-forwarded-for');
  if (forwardedFor) {
    // x-forwarded-for can contain multiple IPs, take the first one
    return forwardedFor.split(',')[0].trim();
  }
  
  const realIP = headers.get('x-real-ip');
  if (realIP) {
    return realIP;
  }
  
  const cfConnectingIP = headers.get('cf-connecting-ip');
  if (cfConnectingIP) {
    return cfConnectingIP;
  }
  
  // Fallback to other headers
  return headers.get('x-client-ip') || 
         headers.get('x-cluster-client-ip') || 
         headers.get('x-forwarded') || 
         headers.get('forwarded-for') || 
         headers.get('forwarded') || 
         undefined;
}

/**
 * Rate limiting store (in-memory for simplicity)
 * In production, consider using Redis or a database
 */
interface RateLimitEntry {
  count: number;
  resetTime: number;
}

const rateLimitStore = new Map<string, RateLimitEntry>();

/**
 * Simple rate limiting implementation
 * @param identifier - Unique identifier (IP address, user ID, etc.)
 * @param maxRequests - Maximum requests allowed
 * @param windowMs - Time window in milliseconds
 * @returns boolean - true if request is allowed, false if rate limited
 */
export function checkRateLimit(
  identifier: string,
  maxRequests: number = 5,
  windowMs: number = 15 * 60 * 1000 // 15 minutes
): boolean {
  const now = Date.now();
  const entry = rateLimitStore.get(identifier);
  
  if (!entry || now > entry.resetTime) {
    // First request or window expired
    rateLimitStore.set(identifier, {
      count: 1,
      resetTime: now + windowMs
    });
    return true;
  }
  
  if (entry.count >= maxRequests) {
    console.warn(`[rate-limit] Rate limit exceeded for ${identifier}: ${entry.count}/${maxRequests}`);
    return false;
  }
  
  // Increment counter
  entry.count++;
  rateLimitStore.set(identifier, entry);
  
  return true;
}

/**
 * Clean up expired rate limit entries
 * Call this periodically to prevent memory leaks
 */
export function cleanupRateLimit(): void {
  const now = Date.now();
  for (const [key, entry] of rateLimitStore.entries()) {
    if (now > entry.resetTime) {
      rateLimitStore.delete(key);
    }
  }
}

// Clean up every 30 minutes
if (typeof setInterval !== 'undefined') {
  setInterval(cleanupRateLimit, 30 * 60 * 1000);
}
