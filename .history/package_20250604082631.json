{"name": "facetrace-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "drizzle-kit generate:pg", "db:push": "drizzle-kit push:pg", "db:studio": "drizzle-kit studio", "db:migrate": "tsx src/lib/db/migrate.ts", "db:introspect": "drizzle-kit introspect:pg", "db:ping": "curl -X GET https://facetrace.pro/api/db-status", "db:ping:local": "curl -X GET http://localhost:3000/api/db-status", "db:stats": "curl -X GET https://facetrace.pro/api/db-status?detailed=true", "db:stats:local": "curl -X GET http://localhost:3000/api/db-status?detailed=true"}, "dependencies": {"@clerk/elements": "^0.23.8", "@clerk/nextjs": "^6.15.1", "@google/generative-ai": "^0.24.0", "@neondatabase/serverless": "^0.9.5", "@radix-ui/react-accordion": "^1.2.3", "@sentry/nextjs": "^9.12.0", "@stripe/react-stripe-js": "^3.6.0", "@stripe/stripe-js": "^6.1.0", "@types/stripe": "^8.0.416", "@types/uuid": "^10.0.0", "axios": "^1.8.4", "browser-image-compression": "^2.0.2", "canvas-confetti": "^1.9.3", "clsx": "^2.1.1", "dotenv": "^16.4.7", "drizzle-kit": "^0.19.1", "drizzle-orm": "^0.29.5", "framer-motion": "^12.7.3", "heic-convert": "^2.1.0", "js-cookie": "^3.0.5", "logrocket": "^10.0.0", "lucide-react": "^0.484.0", "next": "15.3.0", "next-themes": "^0.4.6", "react": "18.3.1", "react-dom": "18.3.1", "react-dropzone": "^14.3.8", "react-google-recaptcha": "^3.1.0", "react-hook-form": "^7.55.0", "react-icons": "^5.5.0", "resend": "^4.3.0", "sharp": "^0.33.5", "stripe": "^17.7.0", "svix": "^1.64.1", "tailwind-merge": "^3.0.2", "ts-node": "^10.9.2", "uuid": "^11.1.0"}, "devDependencies": {"@clerk/types": "^4.54.0", "@tailwindcss/postcss": "^4", "@types/js-cookie": "^3.0.6", "@types/node": "^20", "@types/react": "^19.1.2", "@types/react-dom": "^19", "@types/react-google-recaptcha": "^2.1.9", "autoprefixer": "^10.4.21", "babel-plugin-transform-react-remove-prop-types": "^0.4.24", "eslint": "^9", "eslint-config-next": "15.3.0", "tailwindcss": "^4", "tsx": "^4.19.3", "typescript": "^5"}}