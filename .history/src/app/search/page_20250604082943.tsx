'use client';

import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Image from 'next/image';
import {
  FaInstagram, FaLinkedin, FaFacebook, FaTwitter, FaTiktok, FaYoutube, FaReddit, FaTwitch, FaPinterest, FaGlobe
} from 'react-icons/fa';
import { SiOnlyfans } from 'react-icons/si'; // Using Simple Icons for specific ones (Removed SiPornhub)
import Header from '../components/Header';
import Footer from '../components/Footer';
import CaptchaVerification, { useCaptcha } from '../components/CaptchaVerification';
import {
  uploadPic,
  getSearchResults,
  SearchResult,
  getFacesCount,
  startSearch as apiStartSearch,
  saveFinalResults,
  createPaymentIntent,
  convertHeicToJpeg,
  compressImage
} from '../services/apiService';
import { useAuth, SignedIn } from '@clerk/nextjs';
import { loadStripe } from '@stripe/stripe-js';
import { Elements, PaymentElement, useStripe, useElements } from '@stripe/react-stripe-js';
import ConditionalSignUpModal from '@/components/ConditionalSignUpModal';
import { useRouter } from 'next/navigation';

// At the top of the file with other constants
const DEMO_MODE = process.env.NEXT_PUBLIC_DEMO_MODE === 'false' || false;

// Check if auth is disabled (payment is always disabled in free version)
const isAuthDisabled = process.env.NEXT_PUBLIC_DISABLE_AUTH === 'true';

// Define the steps in the search process
type SearchStep = 'upload' | 'processing' | 'results';

// Define payment form steps
type PaymentStep = 'email' | 'payment' | 'confirm';

// Initialize Stripe promise with the publishable key
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY || 'pk_test_your_key');

// Define CSS for background pattern and gradient
const styles = {
  bgPattern: {
    backgroundColor: '#f7faff',
    backgroundImage: `radial-gradient(circle at 30% 0%, rgba(146, 165, 255, 0.1) 0%, rgba(146, 165, 255, 0) 50%),
    radial-gradient(circle at 70% 100%, rgba(255, 161, 191, 0.1) 0%, rgba(255, 161, 191, 0) 50%)`,
    backgroundSize: '100% 100%',
    backgroundPosition: 'center'
  },
  bgGridPattern: {
    backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg stroke='%23e2e8f0' stroke-width='0.5'%3E%3Cpath d='M0 0h60v60H0z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
    backgroundSize: '30px 30px',
    backgroundColor: '#eef5ff',
    transition: 'all 0.8s ease-in-out'
  },
  gradientBg: {
    background: 'linear-gradient(135deg, #92A5FF 0%, #FFA1BF 100%)'
  },
  faceMeshBg: {
    backgroundImage: `url("data:image/svg+xml,%3Csvg width='300' height='300' viewBox='0 0 300 300' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill='none' stroke='%23ffffff20' stroke-width='1' d='M150,20 L75,280 M150,20 L225,280 M75,280 L225,280 M50,100 L250,100 M80,160 L220,160 M120,40 L120,100 M180,40 L180,100 M100,40 L200,40'/%3E%3C/svg%3E")`,
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    backgroundRepeat: 'no-repeat'
  },
  spotlight: {
    position: 'fixed' as const,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: 'radial-gradient(circle at 50% 40%, transparent 0%, rgba(0,0,0,0.8) 100%)',
    opacity: 0,
    transition: 'opacity 0.6s ease-in-out',
    pointerEvents: 'none' as const,
    zIndex: 1
  },
  aiFaceBg: {
    backgroundImage: `
      radial-gradient(circle at 30% 30%, rgba(146, 165, 255, 0.15) 0%, rgba(146, 165, 255, 0) 70%),
      radial-gradient(circle at 70% 70%, rgba(255, 161, 191, 0.15) 0%, rgba(255, 161, 191, 0) 70%),
      url("data:image/svg+xml,%3Csvg width='800' height='800' viewBox='0 0 800 800' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill='none' stroke='%2392A5FF' stroke-width='1' stroke-opacity='0.2' d='M400,40 L250,680 M400,40 L550,680 M250,680 L550,680 M200,300 L600,300 M250,450 L550,450 M300,100 L300,300 M500,100 L500,300 M300,100 L500,100'/%3E%3C/svg%3E")`,
    backgroundSize: '150% 150%',
    backgroundPosition: 'center',
    opacity: 0.4,
    pointerEvents: 'none',
    zIndex: -1,
    transition: 'opacity 0.8s ease-in-out, background-size 0.8s ease-in-out'
  },
  biometricScanline: {
    height: '2px',
    background: 'linear-gradient(90deg, rgba(0,255,255,0) 0%, rgba(0,255,255,0.8) 50%, rgba(0,255,255,0) 100%)',
    width: '100%',
    position: 'absolute',
    left: 0,
    zIndex: 2
  }
};

// Define AI face background as inline style to avoid type errors
const aiFaceBackground = {
  backgroundImage: `
    radial-gradient(circle at 30% 30%, rgba(146, 165, 255, 0.3) 0%, rgba(146, 165, 255, 0) 70%),
    radial-gradient(circle at 70% 70%, rgba(255, 161, 191, 0.3) 0%, rgba(255, 161, 191, 0) 70%),
    url("data:image/svg+xml,%3Csvg width='1200' height='1200' viewBox='0 0 1200 1200' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill='none' stroke='%2392A5FF' stroke-width='1.5' stroke-opacity='0.35' d='M600,100 L350,900 M600,100 L850,900 M350,900 L850,900 M300,400 L900,400 M350,650 L850,650 M400,200 L400,400 M800,200 L800,400 M400,200 L800,200 M300,280 C300,280 450,320 600,320 C750,320 900,280 900,280 M500,480 C500,480 550,500 600,500 C650,500 700,480 700,480 M450,750 L750,750'/%3E%3Cpath fill='none' stroke='%23FFA1BF' stroke-width='1.5' stroke-opacity='0.2' d='M200,200 L400,250 M300,300 L500,380 M250,350 L450,450 M200,400 L350,520 M200,500 L300,600 M1000,200 L800,250 M900,300 L700,380 M950,350 L750,450 M1000,400 L850,520 M1000,500 L900,600'/%3E%3C/svg%3E")`,
  backgroundSize: '150% 150%',
  backgroundPosition: 'center',
  opacity: 0.7,
  position: 'fixed' as const,
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  zIndex: -1,
  pointerEvents: 'none' as const,
  transition: 'all 0.8s ease-in-out'
};

// Stripe Checkout Modal Component
const StripeCheckoutModal = ({
  isOpen,
  onClose,
  onSuccess,
  price = 5,
  isProcessing = false,
  error = null,
  isSignedIn = false,
  isNewUser = false
}: {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (email: string, clientSecret: string) => void;
  price?: number;
  isProcessing?: boolean;
  error?: string | null;
  isSignedIn?: boolean;
  isNewUser?: boolean;
}) => {
  // State for multi-step form
  const [currentStep, setCurrentStep] = useState<PaymentStep>('email');
  const [email, setEmail] = useState<string>('');
  const [formErrors, setFormErrors] = useState<{[key: string]: string}>({});
  const [paymentError, setPaymentError] = useState<string | null>(error);
  const [isCreatingPaymentIntent, setIsCreatingPaymentIntent] = useState<boolean>(false);
  const [clientSecret, setClientSecret] = useState<string>('');

  // Reset payment error when parent error changes
  useEffect(() => {
    setPaymentError(error);
  }, [error]);

  // Reset form when modal is opened/closed
  useEffect(() => {
    if (isOpen) {
      setCurrentStep('email');
      setPaymentError(null);
      setFormErrors({});
    }
  }, [isOpen]);

  // Handle email form submission
  const handleEmailSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Basic email validation
    if (!email) {
      setFormErrors({ email: 'Email is required' });
      return;
    }

    if (!/\S+@\S+\.\S+/.test(email)) {
      setFormErrors({ email: 'Please enter a valid email' });
      return;
    }

    // Clear errors and proceed to payment step
    setFormErrors({});
    setCurrentStep('payment');

    // Determine which product ID to use based on user status
    let productId;
    if (isSignedIn) {
      if (isNewUser) {
        productId = 'prod_RyPLugzVyqefRu'; // $0.00 for new users
      } else {
        productId = 'prod_S2EXEI0oUk23Ff'; // $5.00 for existing users
      }
    } else {
      productId = 'prod_RyPGgjkL5LfDdr'; // $15.00 for guests
    }

    // Create payment intent when moving to payment step
    try {
      setIsCreatingPaymentIntent(true);

      // Use the createPaymentIntent function from apiService
      const data = await createPaymentIntent(email, {
        service: 'FaceTrace Search',
        userType: isSignedIn ? (isNewUser ? 'new_user' : 'registered_user') : 'guest'
      });

      setClientSecret(data.clientSecret);
    } catch (err: unknown) {
      const error = err as Error;
      setPaymentError(error.message || 'Failed to create payment. Please try again.');
      setCurrentStep('email'); // Go back to email step on error
    } finally {
      setIsCreatingPaymentIntent(false);
    }
  };

  // Options for Stripe Elements
  const appearance = {
    theme: 'stripe' as const,
    variables: {
      colorPrimary: '#92A5FF',
      colorBackground: '#ffffff',
      colorText: '#30313d',
      colorDanger: '#df1b41',
      fontFamily: 'Inter, system-ui, sans-serif',
      spacingUnit: '4px',
      borderRadius: '8px',
    },
  };

  // If modal is not open, don't render anything
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <motion.div
        className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl max-w-md w-full p-6 relative overflow-hidden"
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        transition={{ type: 'spring', damping: 25, stiffness: 300 }}
      >
        {/* Close button - don't allow closing during processing */}
        {currentStep !== 'confirm' && (
          <button
            onClick={() => {
              if (!isProcessing && !isCreatingPaymentIntent) onClose();
            }}
            className="absolute top-3 right-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200"
            disabled={isProcessing || isCreatingPaymentIntent}
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        )}

        {/* Header */}
        <div className="text-center mb-6">
          <div className="h-12 w-12 bg-gradient-to-r from-[#92A5FF] to-[#FFA1BF] rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-1">Complete Your Search</h2>
          <p className="text-gray-600 dark:text-gray-400 text-sm mb-1">
            {currentStep === 'confirm' ? 'Payment Complete!' : 'Guest Face Search Report'}
          </p>
          <p className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-[#92A5FF] to-[#FFA1BF]">
            {price === 0 ? 'FREE' : `$${price.toLocaleString()}.00`}
          </p>
        </div>

        {/* Error message */}
        {paymentError && (
          <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg text-red-600 dark:text-red-400 text-sm">
            <div className="flex items-center">
              <svg className="w-5 h-5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
              <span>{paymentError}</span>
            </div>
          </div>
        )}

        {/* Form steps */}
        {currentStep === 'email' && (
          <motion.form
            onSubmit={handleEmailSubmit}
            className="space-y-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Email
              </label>
              <input
                type="email"
                id="email"
                name="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className={`w-full h-10 px-3 py-2 bg-white dark:bg-gray-800 border ${
                  formErrors.email ? 'border-red-500 dark:border-red-500' : 'border-gray-300 dark:border-gray-600'
                } rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600`}
                disabled={isCreatingPaymentIntent}
              />
              {formErrors.email && (
                <p className="text-red-500 text-xs mt-1">{formErrors.email}</p>
              )}
            </div>

            <button
              type="submit"
              disabled={isCreatingPaymentIntent || !email}
              className={`w-full rounded-lg py-3 font-bold transition-all ${
                isCreatingPaymentIntent || !email
                  ? 'bg-gray-400 dark:bg-gray-600 text-white cursor-not-allowed'
                  : 'bg-gradient-to-r from-[#92A5FF] to-[#FFA1BF] text-white hover:opacity-90'
              }`}
            >
              {isCreatingPaymentIntent ? (
                <span className="flex items-center justify-center">
                  <svg className="animate-spin h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Processing...
                </span>
              ) : (
                'Continue to Payment'
              )}
            </button>
          </motion.form>
        )}

        {currentStep === 'payment' && (
          <>
            {clientSecret ? (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
                className="space-y-6"
              >
                <Elements stripe={stripePromise} options={{ clientSecret, appearance }}>
                  <CheckoutForm
                    email={email}
                    price={price}
                    onSuccess={() => {
                      setCurrentStep('confirm');
                      setTimeout(() => onSuccess(email, clientSecret), 15);
                    }}
                    onError={(message) => setPaymentError(message)}
                  />
                </Elements>
              </motion.div>
            ) : (
              <div className="flex justify-center items-center py-10">
                <svg className="animate-spin h-8 w-8 text-[#92A5FF]" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </div>
            )}
          </>
        )}

        {currentStep === 'confirm' && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
            className="text-center py-6"
          >
            <div className="h-16 w-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Payment Successful!</h3>
            <p className="text-gray-600 dark:text-gray-400 text-sm mb-6">
              Your search is now processing.
            </p>
            <div className="animate-pulse">
              <div className="h-1 w-full bg-gradient-to-r from-[#92A5FF] to-[#FFA1BF] rounded-full"></div>
            </div>
          </motion.div>
        )}

        {/* Security notice */}
        <div className="flex items-center justify-center mt-4 text-xs text-gray-500 dark:text-gray-400">
          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
          Secure payment processed by Stripe
        </div>
      </motion.div>
    </div>
  );
};

// Checkout form component that uses Stripe Elements
const CheckoutForm = ({
  email,
  price,
  onSuccess,
  onError
}: {
  email: string;
  price: number;
  onSuccess: () => void;
  onError: (message: string) => void;
}) => {
  const stripe = useStripe();
  const elements = useElements();
  const [isProcessing, setIsProcessing] = useState(false);
  const [agreedToTerms, setAgreedToTerms] = useState(false);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    // For free products (price === 0), we don't need to process payment
    if (price === 0) {
      onSuccess();
      return;
    }

    if (!stripe || !elements) {
      // Stripe.js has not loaded yet
      return;
    }

    if (!agreedToTerms) {
      onError('Please agree to the terms and conditions');
      return;
    }

    setIsProcessing(true);

    try {
      const { error, paymentIntent } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: window.location.origin,
          receipt_email: email,
        },
        redirect: 'if_required',
      });

      if (error) {
        throw new Error(error.message || 'Something went wrong with your payment');
      }

      if (paymentIntent && paymentIntent.status === 'succeeded') {
        // Payment succeeded!
        onSuccess();
      } else {
        // Payment status is unknown
        throw new Error('Payment status unknown. Please contact support.');
      }
    } catch (err: unknown) {
      const error = err as Error;
      onError(error.message || 'Payment processing failed. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Only show the payment element for paid products */}
      {price > 0 && <PaymentElement />}

      <div className="flex items-start space-x-2">
        <input
          id="terms"
          name="terms"
          type="checkbox"
          checked={agreedToTerms}
          onChange={(e) => setAgreedToTerms(e.target.checked)}
          className="h-4 w-4 mt-1 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
        />
        <label htmlFor="terms" className="text-xs text-gray-500 dark:text-gray-400">
          I agree to the <a href="/terms-of-use" target="_blank" className="text-blue-600 dark:text-blue-400 hover:underline">Terms of Service</a> and <a href="/privacy-policy" target="_blank" className="text-blue-600 dark:text-blue-400 hover:underline">Privacy Policy</a>.
        </label>
      </div>

      <button
        type="submit"
        disabled={(!stripe && price > 0) || isProcessing || !agreedToTerms}
        className={`w-full rounded-lg py-3 font-bold transition-all ${
          (!stripe && price > 0) || isProcessing || !agreedToTerms
            ? 'bg-gray-400 dark:bg-gray-600 text-white cursor-not-allowed'
            : 'bg-gradient-to-r from-[#92A5FF] to-[#FFA1BF] text-white hover:opacity-90'
        }`}
      >
        {isProcessing ? (
          <span className="flex items-center justify-center">
            <svg className="animate-spin h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Processing Payment...
          </span>
        ) : price === 0 ? (
          'Continue for Free'
        ) : (
          `Pay $${price.toLocaleString()}.00`
        )}
      </button>
    </form>
  );
};



export default function SearchPage() {
  // State for managing the upload & search flow
  const [searchStep, setSearchStep] = useState<SearchStep>('upload');
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const [isCompressing, setIsCompressing] = useState(false);
  const [searchProgress, setSearchProgress] = useState<number>(0);
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [imagePreviews, setImagePreviews] = useState<string[]>([]);
  const [dragActive, setDragActive] = useState(false);
  // Add new state for guest email
  const [guestEmail, setGuestEmail] = useState<string>('');
  const [isEmailValid, setIsEmailValid] = useState<boolean>(false);
  
  // Add ref for tracking the current search operation
  const currentSearchRef = useRef<string | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);
  // Add ref for the polling timeout ID
  const pollTimeoutIdRef = useRef<NodeJS.Timeout | null>(null);

  // Authentication state from Clerk - TEMPORARY DISABLE: Mock when auth disabled
  const clerkAuth = isAuthDisabled ? { isSignedIn: false } : useAuth();
  const { isSignedIn } = clerkAuth;
  // Ensure Clerk auth state is properly handled
  const [authInitialized, setAuthInitialized] = useState(isAuthDisabled ? true : false);
  
  // Reference to router for redirects
  const router = useRouter();

  // Access control removed - direct access to search page allowed

  // State for the signup modal
  const [isSignUpModalOpen, setIsSignUpModalOpen] = useState(false);

  useEffect(() => {
    // Mark auth as initialized once isSignedIn is determined
    setAuthInitialized(true);
  }, [isSignedIn]);

  // Network response state
  const [responseMessage, setResponseMessage] = useState<string>('');

  // Advanced background states
  const [backgroundFocus, setBackgroundFocus] = useState(false);

  // Navbar and filtering state
  const [showNavbar, setShowNavbar] = useState(true);
  const [selectedDomains, setSelectedDomains] = useState<string[]>([]);

  // Updated facesCount state
  const [facesCount, setFacesCount] = useState<number | undefined>(undefined);
  const [facesCountStatus, setFacesCountStatus] = useState<'loading' | 'loaded' | 'timeout'>('loading');

  // Add these declarations to satisfy TypeScript - unused in actual code
  // These are only declared to avoid TypeScript errors in commented code sections
  const [isToastVisible, setIsToastVisible] = useState(false);
  const [visibleResults, setVisibleResults] = useState(20);
  const [showStickyNav, setShowStickyNav] = useState(false);
  const resultsContainerRef = useRef<HTMLDivElement>(null);
  const halfwayRef = useRef<HTMLDivElement>(null);
  
  // Placeholder functions to avoid TypeScript errors in commented code
  const handleReturnToUpload = () => {};
  const getDomainStats = () => [] as any[];
  const getPlatformEmoji = (domain: string) => '🌐';

  // Inside the SearchPage component, add state for checkout
  const [isCheckingOut, setIsCheckingOut] = useState(false);
  const [checkoutError, setCheckoutError] = useState<string | null>(null);
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);

  // Free version - no payment bypass needed

  // Animation variants - simplified
  const pageVariants = {
    initial: { opacity: 0 },
    animate: { opacity: 1, transition: { duration: 0.3 } },
    exit: { opacity: 0, transition: { duration: 0.2 } }
  };

  const itemVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.4 } }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        duration: 0.4
      }
    }
  };

  // Scanner animation variants
  const scannerVariants = {
    initial: { top: 0, opacity: 0 },
    scanning: {
      top: ['0%', '100%', '0%'],
      opacity: [0.2, 1, 0.2],
      transition: {
        duration: 3,
        repeat: Infinity,
        ease: "linear"
      }
    }
  };

  // Fetch faces count with timeout
  useEffect(() => {
    // Track if component is mounted
    let isMounted = true;
    
    const fetchFacesCount = async () => {
      try {
        // Set loading state
        setFacesCountStatus('loading');
        
        // Fetch the count
        const count = await getFacesCount();
        
        // Only update state if component is still mounted
        if (isMounted) {
          setFacesCount(count);
          setFacesCountStatus('loaded');
        }
      } catch (error) {
        console.error('[search] Error fetching faces count:', error);
        
        // Only update state if component is still mounted
        if (isMounted) {
          setFacesCountStatus('timeout');
        }
      }
    };

    // Start a timer to detect timeouts
    const timer = setTimeout(() => {
      if (isMounted && facesCountStatus === 'loading') {
        console.warn('[search] Fetching faces count timed out after 15 seconds');
        setFacesCountStatus('timeout');
      }
    }, 15000);

    // Start the fetch
    fetchFacesCount();

    // Cleanup on unmount
    return () => {
      isMounted = false;
      clearTimeout(timer);
    };
  }, []);

  // Handle file selection
  const handleImageSelect = (files: File[]) => {
    setSelectedFiles(files);
    setError(null);

    // Create image previews
    const previews = files.map(file => URL.createObjectURL(file));
    setImagePreviews(previews);
  };

  // Handle file input change
  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const files = Array.from(e.target.files).filter(file =>
        file.type.startsWith('image/') || file.name.toLowerCase().endsWith('.heic')
      );

      if (files.length === 0) {
        setError('Please upload only image files');
        return;
      }

      handleImageSelect(files);
      // Activate background focus when files are selected
      setBackgroundFocus(true);
    }
  };

  // Handle drag events
  const handleDragEnter = (e: React.DragEvent<HTMLLabelElement | HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(true);
    // Don't enable background focus during drag, only after files are dropped
  };

  const handleDragLeave = (e: React.DragEvent<HTMLLabelElement | HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
  };

  const handleDragOver = (e: React.DragEvent<HTMLLabelElement | HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent<HTMLLabelElement | HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const files = Array.from(e.dataTransfer.files).filter(file =>
        file.type.startsWith('image/') || file.name.toLowerCase().endsWith('.heic')
      );

      if (files.length === 0) {
        setError('Please upload only image files');
        return;
      }

      handleImageSelect(files);
      // Activate full background focus when files are dropped
      setBackgroundFocus(true);
    }
  };

  // Function to get search results with progress updates
  const getSearchResultsWithProgress = async (
    searchId: string,
    searchInstanceId: string,
    onProgress: (progress: number, message: string) => void
  ): Promise<SearchResult[]> => {
    // Create a promise that wraps getSearchResults with progress updates
    return new Promise<SearchResult[]>(async (resolve, reject) => {
      // Create a new AbortController for this search operation
      const abortController = new AbortController();
      abortControllerRef.current = abortController;
      
      // Track the last queue position to detect progress
      let lastQueuePosition: number | null = null;
      let consecutiveUnchangedPositions = 0;
      const MAX_UNCHANGED_POSITIONS = 10; // Number of consecutive unchanged positions before considering it stuck
      
      // Reference to the timeout to allow clearing it - REMOVED local variable
      // let pollTimeoutId: NodeJS.Timeout | null = null; // Use pollTimeoutIdRef instead
      
      try {
        console.log(`[SearchPage] Starting search ${searchInstanceId} with ID: ${searchId}`);
        
        // Define the polling function that will recursively call itself
        const pollProgress = async () => {
          // If this search has been cancelled, stop polling
          if (currentSearchRef.current !== searchInstanceId) {
            console.log(`[SearchPage] Search ${searchInstanceId} was superseded, stopping polling`);
            // Ensure timeout is cleared if superseded
            if (pollTimeoutIdRef.current) {
              clearTimeout(pollTimeoutIdRef.current);
              pollTimeoutIdRef.current = null;
            }
            return;
          }
          
          try {
            // Make a status-only request to get progress
            const response = await fetch(`/api/search?action=get-results&id=${searchId}`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                id_search: searchId,
                with_progress: true,
                status_only: false,
                demo: false,
                cached: false
              }),
              signal: abortController.signal
            });

            if (!response.ok) {
              const errorData = await response.json();
              
              // Specifically handle 503 and 504 status codes
              if (response.status === 503 || response.status === 504) {
                console.log(`[SearchPage] Skipping ${response.status} error (${response.statusText}), continuing to poll...`);
                // Show a user-friendly message that we're still trying
                onProgress(0, 'Search service is busy. Continuing to try...');
                // Continue polling with a slightly longer delay
                if (pollTimeoutIdRef.current) clearTimeout(pollTimeoutIdRef.current); // Clear existing before setting new
                pollTimeoutIdRef.current = setTimeout(pollProgress, 5000);
                return;
              }
              
              console.error(`[SearchPage] Error checking progress for ${searchInstanceId}:`, errorData.error || response.statusText);
              
              // Handle specific errors like "still processing" by continuing polling
              if (errorData.error && errorData.error.includes('still being processed')) {
                console.log(`[SearchPage] Search ${searchInstanceId} is still being processed, continuing to poll`);
                onProgress(0, 'Your search is still being processed...');
                // Schedule next poll with longer delay for processing errors
                if (pollTimeoutIdRef.current) clearTimeout(pollTimeoutIdRef.current); // Clear existing before setting new
                pollTimeoutIdRef.current = setTimeout(pollProgress, 5000);
                return;
              }
              
              // For any other errors, throw to be caught below
              throw new Error(errorData.error || response.statusText);
            }

            const data = await response.json();

            // First Check: NO_CREDITS code
            if (data.code === "NO_CREDITS") {
              console.log(`[SearchPage] Search ${searchInstanceId} failed: No credits available`);
              onProgress(0, 'Booting up...');
              
              // KEEP POLLING - don't reject the promise or reset UI
              // This allows the system to recover if credits are added while search is in progress
              if (pollTimeoutIdRef.current) clearTimeout(pollTimeoutIdRef.current); // Clear existing before setting new
              pollTimeoutIdRef.current = setTimeout(pollProgress, 5000); // Poll every 5 seconds
              return;
            }

            // Second Check: QUEUE_BUSY code
            if (data.code === "QUEUE_BUSY") {
              console.log(`[SearchPage] Queue is busy for search ${searchInstanceId}`);
              onProgress(0, 'The search queue is currently busy. Please try again later.');
              // Take back to spotlight and cancel polling
              setSearchStep('upload');
              setBackgroundFocus(false);
              setError('The search queue is currently busy. Please try again later.');
              if (pollTimeoutIdRef.current) {
                clearTimeout(pollTimeoutIdRef.current);
                pollTimeoutIdRef.current = null;
              }
              reject(new Error('Queue is busy. Please try again later.'));
              return;
            }

            // If there's an error but not one of the specific codes we're handling, throw it
            if (data.error) {
              throw new Error(`${data.error} (${data.code})`);
            }

            // Third Check: "Waiting in queue" message - Enhanced with more intelligent queue position handling
            if (data.message && data.message.includes("Waiting in queue")) {
              // Extract queue position if available
              let queuePosition = null;
              const queueMatch = data.message.match(/(\d+)(?:st|nd|rd|th)? place/);
              if (queueMatch && queueMatch[1]) {
                queuePosition = parseInt(queueMatch[1], 10);
                console.log(`[SearchPage] Current queue position: ${queuePosition}`);
              }
              
              // Display the full message to the user
              const queueMessage = data.message.split("queue.")[1]?.trim() || data.message;
              onProgress(0, queueMessage);
              
              // Determine polling interval based on position
              let pollInterval = 10000; // Default 10 seconds
              if (queuePosition === 1 || data.message.includes('1st place')) {
                pollInterval = 3000; // 3 seconds for 1st place
                console.log(`[SearchPage] Queue position is 1st, polling every ${pollInterval/1000} seconds`);
              } else if (queuePosition === 2 || data.message.includes('2nd place')) {
                pollInterval = 3000; // 3 seconds for 2nd place
                console.log(`[SearchPage] Queue position is 2nd, polling every ${pollInterval/1000} seconds`);
              } else if (queuePosition === 3 || data.message.includes('3rd place')) {
                pollInterval = 3000; // 3 seconds for 3rd place
                console.log(`[SearchPage] Queue position is 3rd, polling every ${pollInterval/1000} seconds`);
              } else {
                console.log(`[SearchPage] In queue position ${queuePosition || 'unknown'}, polling every ${pollInterval/1000} seconds`);
              }
              
              // Schedule next poll with appropriate delay
              if (pollTimeoutIdRef.current) clearTimeout(pollTimeoutIdRef.current); // Clear existing before setting new
              pollTimeoutIdRef.current = setTimeout(pollProgress, pollInterval);
              return;
            }

            // Fourth Check: Progress between 0-100 - Enhanced with faster polling for active progress
            if (data.progress !== null && data.progress !== undefined) {
              if (data.progress > 0 && data.progress < 100) {
                // Update progress
                onProgress(data.progress, data.message || 'Processing search...');
                
                // Poll more frequently during active processing (every 1 second)
                if (pollTimeoutIdRef.current) clearTimeout(pollTimeoutIdRef.current); // Clear existing before setting new
                pollTimeoutIdRef.current = setTimeout(pollProgress, 1000);
                return;
              } else if (data.progress === 100 || (data.message && data.message === "Search Completed")) {
                // Progress is 100 or message is "Search Completed"
                if (Array.isArray(data.results) && data.results.length > 0) {
                  // We have actual results - save them
                  onProgress(100, 'Search completed successfully!');
                  console.log(`[SearchPage] Search ${searchInstanceId} completed successfully with ${data.results.length} results`);
                  
                  // Only call save-results when we get "Search Completed" message
                  if (data.message === "Search Completed") {
                    try {
                      // Call save-results API endpoint - Updated to use /api/data
                      const saveResponse = await fetch(`/api/data?action=save-results&id=${searchId}`, {
                        method: 'POST',
                        headers: {
                          'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                          results: data.results
                        })
                      });
                      
                      if (!saveResponse.ok) {
                        console.error(`[SearchPage] Error saving results for search ${searchInstanceId}`);
                      } else {
                        console.log(`[SearchPage] Successfully saved results for search ${searchInstanceId}`);
                      }
                    } catch (saveError) {
                      console.error(`[SearchPage] Error during save-results for search ${searchInstanceId}:`, saveError);
                    }
                  }
                  
                  // Clean up the timeout
                  if (pollTimeoutIdRef.current) {
                    clearTimeout(pollTimeoutIdRef.current);
                    pollTimeoutIdRef.current = null;
                  }
                  
                  // Update report status anyway
                  try {
                    console.log(`[SearchPage] Updating report ${searchId} status to completed with empty results`);
                    await saveFinalResults(searchId, []);
                  } catch (error) {
                    console.error(`[SearchPage] Error updating report status for search ${searchInstanceId}:`, error);
                  }
                  
                  // Resolve with empty results
                  resolve([]);
                  return;
                }
              }
            }

            // Extract queue position from message if it exists (for logging)
            let currentQueuePosition = null;
            if (data.message && data.message.includes('place')) {
              const match = data.message.match(/(\d+)(?:st|nd|rd|th)? place/);
              if (match && match[1]) {
                currentQueuePosition = parseInt(match[1], 10);
                console.log(`[SearchPage] Current queue position: ${currentQueuePosition}`);
              }
            }

            // Log the message to help with debugging
            if (data.message) {
              console.log(`[SearchPage] Server message: "${data.message}"`);
            }
            
            // Check if we're making progress in the queue
            if (currentQueuePosition !== null) {
              if (lastQueuePosition === null || currentQueuePosition < lastQueuePosition) {
                // Queue position is decreasing, which is good
                consecutiveUnchangedPositions = 0;
                lastQueuePosition = currentQueuePosition;
              } else if (currentQueuePosition === lastQueuePosition) {
                // Queue position hasn't changed
                consecutiveUnchangedPositions++;
                console.log(`[SearchPage] Queue position unchanged for ${consecutiveUnchangedPositions} checks`);
                
                // If position hasn't changed for too many checks AND message doesn't contain "queue",
                // reset to prevent being stuck - this ensures we don't consider queue messages as stuck
                if (consecutiveUnchangedPositions >= MAX_UNCHANGED_POSITIONS && 
                    (!data.message || !data.message.toLowerCase().includes('queue'))) {
                  console.log(`[SearchPage] Position appears stuck, will continue polling but resetting counter`);
                  consecutiveUnchangedPositions = 0;
                }
              } else {
                // Queue position increased (shouldn't normally happen)
                console.log(`[SearchPage] Queue position increased from ${lastQueuePosition} to ${currentQueuePosition}`);
                lastQueuePosition = currentQueuePosition;
                consecutiveUnchangedPositions = 0;
              }
            }

            // Update progress only if this is still the current search
            if (currentSearchRef.current === searchInstanceId) {
              if (data.progress && data.progress > 0) {
                onProgress(data.progress, data.message || 'Processing search...');
              } else if (data.message) {
                // Even if progress is 0, we still want to show queue position message
                onProgress(0, data.message);
              }
            }

            // Default polling behavior if none of the specific conditions above were met
            // Schedule next poll if this is still the current search
            if (currentSearchRef.current === searchInstanceId) {
              // Default to 2 seconds
              let delay = 2000;
              if (pollTimeoutIdRef.current) clearTimeout(pollTimeoutIdRef.current); // Clear existing before setting new
              pollTimeoutIdRef.current = setTimeout(pollProgress, delay);
            }

          } catch (error: any) {
            // Ignore AbortError as it's expected when cancelling
            if (error.name === 'AbortError') {
              console.log(`[SearchPage] Polling for search ${searchInstanceId} was aborted`);
              // Ensure timeout is cleared on abort
              if (pollTimeoutIdRef.current) {
                clearTimeout(pollTimeoutIdRef.current);
                pollTimeoutIdRef.current = null;
              }
              return;
            }
            
            // For server errors (5xx), continue polling if we're in an active search state
            if (error.response && error.response.status >= 500 && error.response.status < 600) {
              console.warn(`[SearchPage] Server error ${error.response.status} during polling for search ${searchInstanceId}, continuing to poll`);
              
              // Even on error, continue polling if this is still the current search
              if (currentSearchRef.current === searchInstanceId) {
                // Use default delay on error
                if (pollTimeoutIdRef.current) clearTimeout(pollTimeoutIdRef.current); // Clear existing before setting new
                pollTimeoutIdRef.current = setTimeout(pollProgress, 5000); // Longer delay for server errors
              }
              return;
            }
            
            // Check if we're in third scenario (waiting in queue) or fourth scenario (progress > 0)
            const isInThirdOrFourthScenario = 
              responseMessage.includes('place') || // In queue
              (searchProgress > 0 && searchProgress < 100); // Progress between 0-100
            
            // For third or fourth scenarios, skip any error and continue polling
            if (isInThirdOrFourthScenario) {
              console.log(`[SearchPage] In queue or processing state, skipping error and continuing to poll`);
              onProgress(searchProgress, 'Search service is busy. Continuing to try...');
              
              if (currentSearchRef.current === searchInstanceId) {
                const pollInterval = responseMessage.includes('place') ? 10000 : 1000; // 10s for queue, 1s for progress
                if (pollTimeoutIdRef.current) clearTimeout(pollTimeoutIdRef.current); // Clear existing before setting new
                pollTimeoutIdRef.current = setTimeout(pollProgress, pollInterval);
                return;
              }
            }
            
            // Specifically handle 503 Service Unavailable and 504 Gateway Timeout errors
            if (error.message && (
                error.message.includes('503') || 
                error.message.includes('504') ||
                error.message.includes('Service Unavailable') ||
                error.message.includes('Gateway Timeout')
            )) {
              console.log(`[SearchPage] Skipping ${error.message.includes('503') ? '503' : '504'} error, continuing to poll...`);
              onProgress(searchProgress, 'Search service is busy. Continuing to try...');
              if (currentSearchRef.current === searchInstanceId) {
                if (pollTimeoutIdRef.current) clearTimeout(pollTimeoutIdRef.current); // Clear existing before setting new
                pollTimeoutIdRef.current = setTimeout(pollProgress, 5000); // 5 second delay for these specific errors
              }
              return;
            }
            
            console.error(`[SearchPage] Error checking search progress for ${searchInstanceId}:`, error);
            
            // For other errors, log them but keep polling if we're in active search modes
            // (but don't continue for QUEUE_BUSY which are handled above)
            if (currentSearchRef.current === searchInstanceId && 
                !error.message.includes('Queue is busy')) {
              if (pollTimeoutIdRef.current) clearTimeout(pollTimeoutIdRef.current); // Clear existing before setting new
              pollTimeoutIdRef.current = setTimeout(pollProgress, 5000);
            }
          }
        };
        
        // Start the polling process
        pollProgress();

        // Get the actual results
        try {
          const results = await getSearchResults(searchId, abortController.signal);

          // Clean up the timeout
          if (pollTimeoutIdRef.current) {
            clearTimeout(pollTimeoutIdRef.current);
            pollTimeoutIdRef.current = null;
          }

          // If this search was superseded by a new one, reject the promise
          if (currentSearchRef.current !== searchInstanceId) {
            console.log(`[SearchPage] Search ${searchInstanceId} completed but was superseded`);
            reject(new Error('Search was superseded by a newer search'));
            return;
          }

          console.log(`[SearchPage] Search ${searchInstanceId} completed successfully`);
          
          // Return the results
          resolve(results);

        } catch (error: any) {
          // Clean up the timeout
          if (pollTimeoutIdRef.current) {
            clearTimeout(pollTimeoutIdRef.current);
            pollTimeoutIdRef.current = null;
          }
          
          // Ignore AbortError as it's expected when cancelling
          if (error.name === 'AbortError') {
            console.log(`[SearchPage] Search ${searchInstanceId} was aborted`);
            reject(new Error('Search was aborted'));
          } else {
            console.error(`[SearchPage] Error getting search results for ${searchInstanceId}:`, error);
            reject(error);
          }
        }
      } catch (error) {
        reject(error);
      }
    });
  };

  // Handle search initiation - now follows "search first, unlock later" flow
  const handleSearch = async () => {
    try {
      if (selectedFiles.length === 0) {
        setError('Please upload at least one image to search');
        return;
      }

      // Validate email for guest users
      if (authInitialized && !isSignedIn) {
        if (!guestEmail || !isEmailValid) {
          setError('Please enter a valid email address');
          return;
        }
        console.log('[search] Guest user starting search with email:', guestEmail);
      }

      // Immediately proceed with search without payment - payment happens at unlock stage
      proceedWithSearch(false, guestEmail);
    } catch (error) {
      console.error('Error during search:', error);
      setError('An error occurred during the search. Please try again.');
      setResponseMessage('Search failed. Please try again.');
      setSearchStep('upload');
      setIsUploading(false);
      setIsSearching(false);
    }
  };

  // New function to handle actual search after payment
  const proceedWithSearch = async (deductToken = false, guestEmail: string = '', paymentClientSecret: string = '') => {
    // Cancel any ongoing search operations
    if (abortControllerRef.current) {
      console.log('[SearchPage] Cancelling previous search operation');
      abortControllerRef.current.abort();
    }
    
    // Generate a unique ID for this search instance
    const searchInstanceId = Date.now().toString();
    currentSearchRef.current = searchInstanceId;
    
    console.log(`[SearchPage] Starting new search with instance ID: ${searchInstanceId}`);
    setError(null);
    setIsUploading(true);
    setSearchProgress(0);
    setResponseMessage('Preparing search...');
    setSearchStep('processing');
    // Activate background focus when processing begins
    setBackgroundFocus(true);

    // Track payment and database information
    let searchReportId: string | null = null;
    let transactionId: number | null = null;
    let price = 5; // Fixed at $5 for all transactions
    let saveSuccessful = false; // Declare the flag here

    // All transactions are treated as guest transactions
    let userType = "guest";
    let userEmail = guestEmail; // Use the email passed from checkout

    try {
      // Upload images and get search IDs
      const searchPromises = [];
      for (const file of selectedFiles) {
        try {
          // Process the file before uploading
          setResponseMessage(`Processing image: ${file.name}`);

          // Convert HEIC to JPEG if needed
          let processedFile = file;
          if (file.type === 'image/heic' || file.name.toLowerCase().endsWith('.heic')) {
            setResponseMessage(`Converting HEIC image: ${file.name}`);
            processedFile = await convertHeicToJpeg(file);
          }

          // Compress the image if needed
          setIsCompressing(true);
          setResponseMessage(`Compressing image: ${processedFile.name}`);
          const compressedFile = await compressImage(processedFile);
          setIsCompressing(false);

          // Upload the processed image
          setResponseMessage(`Uploading image: ${compressedFile.name}`);
          const searchId = await uploadPic(compressedFile);
          searchPromises.push(searchId);
        } catch (error) {
          console.error(`Error processing file ${file.name}:`, error);
          throw error;
        }
      }

      const searchIds = await Promise.all(searchPromises);

      // Use the provided client secret from the payment if available
      // Otherwise use a dummy one for development/testing
      const clientSecret = paymentClientSecret || ('pi_mock_' + Date.now().toString() + '_secret_mock');

      try {
        // Start the search with the payment verification
        // Pass all searchIds to preserve them for searchImageUrls
        const startSearchResponse = await apiStartSearch(
          searchIds.length > 0 ? searchIds[0] : '', 
          clientSecret, 
          userEmail
        );

        if (!startSearchResponse.success) {
          throw new Error('Failed to start search');
        }

        searchReportId = startSearchResponse.reportId;
        const report_id = startSearchResponse.report_id; // Get the report_id for URLs
        
        // We've already uploaded the images and started the search
        setIsUploading(false);
        setIsSearching(true);
        setSearchProgress(0);
        setResponseMessage('Upload complete. Starting search...');

        // Set up progress listener for search
        const progressListener = (progress: number, message: string) => {
          // Only update progress if this is still the current search
          if (currentSearchRef.current === searchInstanceId) {
            if (progress > 0) {
              setSearchProgress(progress);
            }
            if (message) {
              setResponseMessage(message);
            }
          }
        };

        // Start search for each image and collect results
        const resultsPromises = searchIds.map(async (searchId) => {
          try {
            // Use a modified search function that exposes progress updates
            return await getSearchResultsWithProgress(searchId, searchInstanceId, progressListener);
          } catch (error) {
            // If this search was superseded, ignore the error
            if (currentSearchRef.current !== searchInstanceId) {
              console.log(`[SearchPage] Ignoring error for superseded search ${searchInstanceId}`);
              return [];
            }
            
            console.error(`[SearchPage] Error getting results for search ID ${searchId} (instance ${searchInstanceId}):`, error);
            throw error;
          }
        });

        // Check if this search is still the current one
        if (currentSearchRef.current === searchInstanceId) {
          const allResults = await Promise.all(resultsPromises);
    
          // Only process results if this is still the current search
          if (currentSearchRef.current === searchInstanceId) {
            // Combine and deduplicate results from all searches
            const combinedResults = allResults.flat();
            const uniqueResults = Array.from(
              new Map(combinedResults.map((item: SearchResult) => [item.id, item])).values()
            );
      
            // Sort by confidence (highest first)
            const sortedResults = uniqueResults.sort((a, b) => (b as SearchResult).confidence - (a as SearchResult).confidence);
      
            // Save search results to the database
            if (searchReportId) {
              try {
                // First update the report status to complete
                // The API will handle this when we save the results
                
                // Track if the save was successful
                let isSaveSuccessful = false;
                let saveError = null;
                let saveStatus = null;
                
                // Save the results using our API service with max retry attempts
                try {
                  const saveResult = await saveFinalResults(searchReportId, sortedResults);
                  isSaveSuccessful = saveResult.success === true;
                  saveStatus = saveResult.status;
                  console.log(`[SearchPage] Save result for report ${searchReportId}: success=${isSaveSuccessful}, status=${saveStatus}`);
                } catch (error) {
                  saveError = error;
                  console.error(`[SearchPage] Error saving search results for report ${searchReportId}:`, error);
                }
                
                // Double-check if save failed by querying the report status
                if (!isSaveSuccessful && !saveStatus) {
                  try {
                    console.log(`[SearchPage] Verifying report status after save error`);
                    const reportCheck = await fetch(`/api/reports/${report_id}/results`);
                    if (reportCheck.ok) {
                      const reportData = await reportCheck.json();
                      console.log(`[SearchPage] Report ${report_id} status check: ${reportData.status}`);
                      
                      // If the report shows as completed despite the save error, consider it successful
                      if (reportData.status === 'completed') {
                        console.log(`[SearchPage] Report is already marked as completed despite save error`);
                        isSaveSuccessful = true;
                        saveStatus = 'completed';
                      }
                    }
                  } catch (checkError) {
                    console.error(`[SearchPage] Error checking report status:`, checkError);
                  }
                }
                
                saveSuccessful = isSaveSuccessful;
                console.log(`[SearchPage] Final save status for report ${searchReportId}: ${saveSuccessful ? 'SUCCESS' : 'FAILED'}`);
                
                if (!saveSuccessful) {
                  // Even if the status update fails, we can still show the results to the user
                  // But set an error message
                  setError("Warning: Results may not be saved properly. The report might be incomplete.");
                }
              } catch (error) {
                console.error(`[SearchPage] Final error in save handling (instance ${searchInstanceId}):`, error);
                // Set error state even if saving fails, but allow redirect if report_id exists
                setError("Failed to save results, but search completed. Report might be incomplete.");
              }
            } else {
               console.warn(`[SearchPage] No searchReportId available, cannot save results (instance ${searchInstanceId})`);
               setError("Search completed, but could not save results (missing report ID).");
            }

            // Only update UI and redirect if this is still the current search
            if (currentSearchRef.current === searchInstanceId) {
              setSearchResults(sortedResults as SearchResult[]);
              setSearchProgress(100);
              setIsSearching(false); // Stop search indicator

              // Check if report_id exists before attempting redirect
              if (report_id) {
                // Schedule redirect after a short delay regardless of save success
                // The API will handle fallbacks for incomplete reports
                setResponseMessage('Search complete! Redirecting to results...');
                console.log(`[SearchPage] Search complete, scheduling redirect to report: /r/${report_id} (instance ${searchInstanceId})`);
                
                // Use a longer delay if there were save issues to give error messages time to display
                const redirectDelay = saveSuccessful ? 1000 : 2500;
                setTimeout(() => {
                   if (currentSearchRef.current === searchInstanceId) {
                      router.push(`/r/${report_id}`);
                   }
                }, redirectDelay);
              } else {
                // Handle case where report_id is missing
                console.error(`[SearchPage] Search completed but report_id is missing (instance ${searchInstanceId})`);
                  setError("Search complete, but could not generate report link. Please try again.");
                  // Go back to upload step on error after a delay
                  setTimeout(() => {
                     if (currentSearchRef.current === searchInstanceId) {
                        setSearchStep('upload');
                        setBackgroundFocus(false); // Reset background focus
                     }
                  }, 1500);
              }
            } else {
               console.log(`[SearchPage] Search ${searchInstanceId} completed but was superseded before redirect`);
            }
          } else {
            console.log(`[SearchPage] Search ${searchInstanceId} was superseded during processing`);
          }
        } else {
          console.log(`[SearchPage] Search ${searchInstanceId} was superseded before processing results`);
        }
      } catch (error: any) {
        // Handle the specific error for pending searches
        if (error.message && (
          error.message.includes('still being processed') || 
          error.message.includes('wait a before')
        )) {
          console.error(`[SearchPage] Error starting search: ${error.message}`);
          setError('Your last search is still being processed. Please wait before submitting a new search.');
          setResponseMessage('Previous search still in progress');
          setSearchStep('upload');
          setIsUploading(false);
          setIsSearching(false);
          setBackgroundFocus(false);
          return;
        }
        throw error; // Re-throw other errors for general error handling
      }
    } catch (error: any) {
      console.error(`[SearchPage] Error during search (instance ${searchInstanceId}):`, error);
      
      // Only update UI if this is still the current search
      if (currentSearchRef.current === searchInstanceId) {
        setError(error.message || 'An error occurred during the search. Please try again.');
        setResponseMessage('Search failed. Please try again.');
        setSearchStep('upload');
        setIsUploading(false);
        setIsSearching(false);
        setBackgroundFocus(false);
      }
    }
  };

  // Handle successful payment
  const handlePaymentSuccess = (guestEmail: string, clientSecret: string) => {
    // First simulate processing the payment
    setIsProcessingPayment(true);

    // With real Stripe integration, payment has already been processed
    // by the time this function is called

    // Proceed with the search - no need to simulate anymore
    setIsProcessingPayment(false);
    setIsCheckingOut(false);
    setCheckoutError(null);

    // Proceed with the search, passing the guest email and client secret
    proceedWithSearch(false, guestEmail, clientSecret);
  };

  // Handle payment cancellation
  const handlePaymentCancel = () => {
    if (isProcessingPayment) return; // Don't allow cancellation during processing
    setIsCheckingOut(false);
  };

  // Handle payment errors (for future integration with actual Stripe)
  const handlePaymentError = (errorMessage: string) => {
    setIsProcessingPayment(false);
    setCheckoutError(errorMessage);
  };

  // Track selected files to reset spotlight if no files are selected
  useEffect(() => {
    if (selectedFiles.length === 0 && searchStep === 'upload') {
      setBackgroundFocus(false);
    } else if (selectedFiles.length > 0 || searchStep !== 'upload') {
      setBackgroundFocus(true);
    }
  }, [selectedFiles, searchStep]);

  // Control navbar visibility based on backgroundFocus
  useEffect(() => {
    setShowNavbar(!backgroundFocus);
  }, [backgroundFocus]);

  // Cleanup image previews on unmount
  useEffect(() => {
    return () => {
      imagePreviews.forEach(preview => URL.revokeObjectURL(preview));
    };
  }, [imagePreviews]);

  // Developer keyboard shortcut to toggle payment bypass (Shift+D)
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Check for Shift+D combination
      if (e.shiftKey && e.key === 'D') {
        // Check if we're in production mode
        const isProduction = process.env.NODE_ENV === 'production' || 
                             window.location.hostname === 'facetrace.pro' ||
                             window.location.hostname.includes('.vercel.app');
                             
        if (isProduction) {
          console.warn('Payment bypass is disabled in production environments');
          // Show a toast notification to inform the user
          setResponseMessage('Payment bypass is disabled in production environments');
          setIsToastVisible(true);
          // Hide toast after 3 seconds
          setTimeout(() => {
            setIsToastVisible(false);
          }, 3000);
          return; // Don't allow bypass in production
        }
        
        // Free version - no payment bypass needed
        console.log('%cFREE VERSION: Payment is always disabled',
          'background: #4CAF50; color: white; padding: 2px 5px; border-radius: 3px; font-weight: bold;');
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  // Reset search
  const handleReset = () => {
    // First reset backgroundFocus for smooth animation
    setBackgroundFocus(false);

    // Short delay before changing other state to allow animation to complete
    setTimeout(() => {
      setSearchStep('upload');
      setSelectedFiles([]);
      setImagePreviews([]);
      setSearchResults([]);
      setSearchProgress(0);
      setError(null);
    }, 300);
  };

  // Set up intersection observer for loading more results when scrolling
  // Remove useEffect for intersection observer (pagination)
  /*
  useEffect(() => {
    if (!halfwayRef.current) return;

    const currentFilteredResults = selectedDomains.length === 0
      ? searchResults
      : searchResults.filter(r => selectedDomains.includes(extractDomain(r.sourceUrl)));

    if (currentFilteredResults.length <= visibleResults) return;

    const observer = new IntersectionObserver(
      entries => {
        const [entry] = entries;
        if (entry.isIntersecting && !isLoadingMore) {
          // Start loading more results when halfway through the current results
          setIsLoadingMore(true);
          setIsToastVisible(true);

          // Hide toast after 2 seconds
          setTimeout(() => {
            setIsToastVisible(false);
          }, 2000);

          // Simulate loading delay
          setTimeout(() => {
            const filteredResults = selectedDomains.length === 0
              ? searchResults
              : searchResults.filter(r => selectedDomains.includes(extractDomain(r.sourceUrl)));

            // Check if we've loaded a lot of results already
            if (visibleResults >= 30 && filteredResults.length > 50) {
              setShowLoadAllPrompt(true);
              setIsLoadingMore(false);
            } else {
              // Load next batch - load 10 more at a time
              setVisibleResults(prev => Math.min(prev + 10, filteredResults.length));
              setIsLoadingMore(false);
            }
          }, 800);
        }
      },
      { threshold: 0.1 } // Trigger when 10% of the element is visible
    );

    observer.observe(halfwayRef.current);

    return () => {
      observer.disconnect();
    };
  }, [halfwayRef, searchResults, selectedDomains, visibleResults, isLoadingMore]);
  */

  // Handle scroll to show/hide sticky navigation
  // Remove useEffect for sticky navigation
  /*
  useEffect(() => {
    if (!resultsContainerRef.current) return;

    let lastScrollY = window.scrollY;

    const handleScroll = () => {
      const currentScrollY = window.scrollY;

      // Show sticky nav when scrolling down past the initial viewport
      if (currentScrollY > window.innerHeight * 0.3) {
        // Only show when scrolling down, hide when scrolling up
        if (currentScrollY > lastScrollY) {
          setShowStickyNav(true);
        } else {
          setShowStickyNav(false);
        }
      } else {
        setShowStickyNav(false);
      }

      lastScrollY = currentScrollY;
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [resultsContainerRef]);
  */

  // Helper function to extract domain from URL - Keep for now, check usage
  const extractDomain = (url: string): string => {
    try {
      const domain = new URL(url).hostname.replace('www.', '');

      // For well-known sites, return the platform name instead of the domain
      if (domain.includes('facebook.com')) return 'Facebook';
      if (domain.includes('instagram.com')) return 'Instagram';
      if (domain.includes('twitter.com') || domain.includes('x.com')) return 'Twitter';
      if (domain.includes('linkedin.com')) return 'LinkedIn';
      if (domain.includes('tiktok.com')) return 'TikTok';
      if (domain.includes('pinterest.com')) return 'Pinterest';
      if (domain.includes('youtube.com') || domain.includes('youtu.be')) return 'YouTube';
      if (domain.includes('reddit.com')) return 'Reddit';
      if (domain.includes('tumblr.com')) return 'Tumblr';
      if (domain.includes('snapchat.com')) return 'Snapchat';
      if (domain.includes('github.com')) return 'GitHub';
      if (domain.includes('medium.com')) return 'Medium';
      if (domain.includes('gov')) return 'Government';
      if (domain.includes('edu')) return 'Education';

      return domain;
    } catch {
      return 'Unknown';
    }
  };

  // Get platform emoji
  // Remove getPlatformEmoji (specific to inline results)
  /*
  const getPlatformEmoji = (domain: string): string => {
    if (domain === 'Facebook') return '👤';
    if (domain === 'Instagram') return '📷';
    if (domain === 'Twitter') return '🐦';
    if (domain === 'LinkedIn') return '💼';
    if (domain === 'TikTok') return '🎵';
    if (domain === 'Pinterest') return '📌';
    if (domain === 'YouTube') return '▶️';
    if (domain === 'Reddit') return '🔍';
    if (domain === 'Tumblr') return '📝';
    if (domain === 'Snapchat') return '👻';
    if (domain === 'GitHub') return '💻';
    if (domain === 'Medium') return '📰';
    if (domain === 'Government') return '🏛️';
    if (domain === 'Education') return '🎓';
    return '🌐';
  };
  */

  // Get domain stats from search results
  // Remove getDomainStats (specific to inline results)
  /*
  const getDomainStats = () => {
    const domainMap = new Map<string, { count: number, maxScore: number }>();

    searchResults.forEach(result => {
      const domain = extractDomain(result.sourceUrl);

      if (!domainMap.has(domain)) {
        domainMap.set(domain, { count: 1, maxScore: result.confidence });
      } else {
        const existing = domainMap.get(domain)!;
        existing.count += 1;
        if (result.confidence > existing.maxScore) {
          existing.maxScore = result.confidence;
        }
        domainMap.set(domain, existing);
      }
    });

    // Convert to array and sort by max score (descending)
    return Array.from(domainMap.entries())
      .map(([domain, stats]) => ({
        domain,
        count: stats.count,
        maxScore: stats.maxScore,
        emoji: getPlatformEmoji(domain) // Depends on removed function
      }))
      .sort((a, b) => b.maxScore - a.maxScore);
  };
  */

  // Return to upload screen
  // Remove handleReturnToUpload (specific to inline results)
  /*
  const handleReturnToUpload = () => {
    setSearchStep('upload');
    // Reset files but keep results in case user wants to see them again
    setSelectedFiles([]);
    setImagePreviews([]);
    setSearchProgress(0);
    setError(null);
    setBackgroundFocus(false);
  };
  */

  // Simplified user state management
  const [isNewUser, setIsNewUser] = useState<boolean>(false);
  const [remainingTokens, setRemainingTokens] = useState<number>(0);
  const [isTokenLoading, setIsTokenLoading] = useState<boolean>(false);

  // Simplified token check function
  const checkUserTokens = async (): Promise<number> => {
    if (!isSignedIn) return 0;

    try {
      setIsTokenLoading(true);
      const response = await fetch('/api/data?action=user-data', {
        method: 'GET',
        cache: 'no-store',
      });

      if (response.ok) {
        const data = await response.json();
        const tokens = data.tokens || 0;

        setIsNewUser(tokens > 0);
        setRemainingTokens(tokens);
        setIsTokenLoading(false);
        return tokens;
      } else {
        console.error(`Token fetch failed with status: ${response.status}`);
        setIsTokenLoading(false);
        return 0;
      }
    } catch (error) {
      console.error('Error checking user tokens:', error);
      setIsTokenLoading(false);
      return 0;
    }
  };

  // Effect for initial check when auth is initialized
  useEffect(() => {
    if (authInitialized && isSignedIn) {
      checkUserTokens();
    }
  }, [authInitialized, isSignedIn]);

  // Effect for tab visibility changes
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && authInitialized && isSignedIn) {
        console.log("Tab is visible, refreshing token count");
        checkUserTokens();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [authInitialized, isSignedIn]);

  // Simplified polling effect
  useEffect(() => {
    if (!authInitialized || !isSignedIn) return;

    const interval = setInterval(() => {
      checkUserTokens();
    }, 15000); // Check every 15 seconds

    return () => clearInterval(interval);
  }, [authInitialized, isSignedIn]);

  // Handle sign up modal success
  const handleSignUpSuccess = async () => {
    setIsSignUpModalOpen(false);

    // Check user's token count to confirm free tokens
    const tokenCount = await checkUserTokens();
    console.log(`[handleSignUpSuccess] Token check complete. User has ${tokenCount} tokens after signup.`);

    // After confirming user status, proceed with search using credit
    if (selectedFiles.length > 0) {
      // Generate a placeholder client secret for token-based searches
      const tokenBasedClientSecret = `pi_token_${Date.now()}_secret_${Math.random().toString(36).substring(2, 9)}`;

      console.log(`[handleSignUpSuccess] Starting token-based search with client secret: ${tokenBasedClientSecret}`);

      // Execute search with the free credit, passing a dummy clientSecret and user's email
      proceedWithSearch(true, '', tokenBasedClientSecret);
    }
  };

  // Determine the price based on user status - now always $5
  const getCheckoutPrice = () => {
    return 5; // Fixed at $5 for all transactions
  };

  // Get the price display for the header
  const getPriceDisplay = () => {
    return "$5"; // Fixed at $5 for all transactions
  };

  // Add this useEffect hook inside the SearchPage component, with other useEffect hooks
  // Effect to handle marquee vs. centered pills
  useEffect(() => {
    // Skip on server-side rendering
    if (typeof window === 'undefined') return;

    const applyResponsiveStyles = () => {
      const container = document.querySelector('.social-pills-container');
      const pillsWrapper = container?.querySelector('.social-pills-wrapper');

      if (!pillsWrapper) return;

      // Check if we're on mobile (screen width less than 768px)
      const isMobile = window.innerWidth < 768;

      if (isMobile) {
        // On mobile: apply marquee animation and remove centering
        pillsWrapper.classList.add('animate-marquee');
        pillsWrapper.classList.remove('justify-center');
      } else {
        // On desktop: remove marquee animation and add centering
        pillsWrapper.classList.remove('animate-marquee');
        pillsWrapper.classList.add('justify-center');
      }
    };

    // Apply initially after a short delay
    setTimeout(applyResponsiveStyles, 100);

    // Add resize listener to adjust on screen size changes
    window.addEventListener('resize', applyResponsiveStyles);

    // Cleanup function to remove event listener
    return () => {
      window.removeEventListener('resize', applyResponsiveStyles);
    };

  }, []);  // Empty dependency array means this runs once after mount

  // Add this effect to handle the social media icon animations
  useEffect(() => {
    // Skip on server-side
    if (typeof window === 'undefined') return;

    let animationTimer: NodeJS.Timeout | null = null;
    let initialDelayTimer: NodeJS.Timeout | null = null;
    let currentIconIndex = 0;
    const totalIcons = 14; // Total number of social media icons
    const animationDelay = 3000; // 3 second initial delay
    const cycleInterval = 2000; // 2 seconds between cycling icons

    const icons = document.querySelectorAll('[data-social-icon]');
    if (icons.length === 0) return;

    // Function to update active icon
    const updateActiveIcon = () => {
      // Reset all icons first
      icons.forEach((icon) => {
        icon.classList.remove('social-icon-expanded');
        icon.classList.add('w-8');
      });

      // Expand the current icon
      const currentIcon = icons[currentIconIndex];
      if (currentIcon) {
        currentIcon.classList.add('social-icon-expanded');
        currentIcon.classList.remove('w-8');
      }

      // Move to the next icon
      currentIconIndex = (currentIconIndex + 1) % totalIcons;
    };

    // Step 1: Initialize first three icons as expanded
    const initializeFirstThreeIcons = () => {
      // Reset all icons first
      icons.forEach((icon) => {
        icon.classList.remove('social-icon-expanded');
        icon.classList.add('w-8');
      });
      
      // Expand first three icons
      for (let i = 0; i < Math.min(10, icons.length); i++) {
        icons[i].classList.add('social-icon-expanded');
        icons[i].classList.remove('w-8');
      }
    };

    // Step 1: Run initialization immediately
    initializeFirstThreeIcons();

    // Step 2 & 3: After delay, reset icons and start cycling
    initialDelayTimer = setTimeout(() => {
      // Step 3: Reset all icons including the first three
      icons.forEach((icon) => {
        icon.classList.remove('social-icon-expanded');
        icon.classList.add('w-8');
      });
      
      // Step 4: Start the animation loop from the first icon
      currentIconIndex = 0; 
      updateActiveIcon(); // Show the first icon immediately
      
      // Continue cycling through icons at regular intervals
      animationTimer = setInterval(updateActiveIcon, cycleInterval);

    }, animationDelay);

    // Cleanup function
    return () => {
      if (initialDelayTimer) clearTimeout(initialDelayTimer);
      if (animationTimer) clearInterval(animationTimer);
    };
  }, []);

  // Add useEffect for cleanup on unmount
  useEffect(() => {
    // Return a cleanup function
    return () => {
      console.log('[SearchPage] Component unmounting, cleaning up polling...');
      // Abort any ongoing fetch requests associated with the polling
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
        console.log('[SearchPage] Aborted ongoing fetch request.');
      }
      // Clear any scheduled polling timeout
      if (pollTimeoutIdRef.current) {
        clearTimeout(pollTimeoutIdRef.current);
        pollTimeoutIdRef.current = null;
        console.log('[SearchPage] Cleared pending polling timeout.');
      }
      // Optionally reset the current search ref
      currentSearchRef.current = null;
    };
  }, []); // Empty dependency array means this runs only on mount and unmount

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-blue-50 to-white dark:from-gray-900 dark:to-gray-800 relative overflow-hidden" style={styles.bgPattern}>
      {/* Background Gradient Overlay */}
      <div className="absolute inset-0 pointer-events-none z-0 opacity-30"
        style={{
          backgroundImage: `radial-gradient(circle at 30% 0%, rgba(146, 165, 255, 0.1) 0%, rgba(146, 165, 255, 0) 50%),
            radial-gradient(circle at 70% 100%, rgba(255, 161, 191, 0.1) 0%, rgba(255, 161, 191, 0) 50%)`,
          backgroundSize: '100% 100%',
          backgroundPosition: 'center'
        }}
      />

      {/* Free production version - no testing indicators needed */}

      {/* Decorative elements */}
      <div className="absolute top-0 right-0 w-72 h-72 bg-blue-200/20 rounded-full -mr-36 -mt-36 blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-64 h-64 bg-blue-300/10 rounded-full -ml-32 -mb-32 blur-3xl"></div>

      {/* AI Face Background with dynamic opacity and size based on focus */}
      <div style={{
        ...aiFaceBackground,
        opacity: backgroundFocus ? 0.9 : 0.4,
        backgroundSize: backgroundFocus ? '120% 120%' : '150% 150%',
        transition: 'opacity 0.8s ease-in-out, background-size 0.8s ease-in-out',
        zIndex: -1
      }}></div>

      {/* Spotlight effect for processing stage */}
      <div style={{
        ...styles.spotlight,
        opacity: backgroundFocus ? 0.8 : 0,
        zIndex: 0
      }}></div>

      {/* Biometric grid overlay that appears during focused upload/search */}
      {backgroundFocus && (
        <div className="fixed inset-0 pointer-events-none z-0 opacity-20 dark:opacity-30"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='%2392A5FF' stroke-width='0.5' fill='none' d='M0,0 L40,0 L40,40 L0,40 L0,0 Z M10,0 L10,40 M20,0 L20,40 M30,0 L30,40 M0,10 L40,10 M0,20 L40,20 M0,30 L40,30'/%3E%3C/svg%3E")`,
            backgroundSize: '40px 40px'
          }}
        />
      )}

      {/* Header with smooth transition */}
      <div className="transition-opacity duration-500 ease-in-out"
           style={{ opacity: searchStep === 'upload' && !backgroundFocus ? 1 : 0, pointerEvents: searchStep === 'upload' && !backgroundFocus ? 'auto' : 'none' }}>
        {searchStep === 'upload' && <Header />}
      </div>

      {/* Custom Sign Up Modal - TEMPORARY DISABLE: Conditionally rendered when auth is enabled */}
      <ConditionalSignUpModal
        isOpen={isSignUpModalOpen}
        onClose={() => setIsSignUpModalOpen(false)}
        onSuccess={handleSignUpSuccess}
      />

      {/* Stripe Checkout Modal */}
      <StripeCheckoutModal
        isOpen={isCheckingOut}
        onClose={handlePaymentCancel}
        onSuccess={handlePaymentSuccess}
        price={getCheckoutPrice()}
        isProcessing={isProcessingPayment}
        error={checkoutError}
        isSignedIn={isSignedIn}
        isNewUser={isNewUser}
      />

      {/* Error message for checkout */}
      {checkoutError && (
        <div className="fixed top-5 left-1/2 transform -translate-x-1/2 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg z-50">
          {checkoutError}
        </div>
      )}

      <main className={`flex-1 ${searchStep === 'upload' ? 'py-5 md:py-8' : 'py-10 md:py-16'}`}>
        <AnimatePresence mode="wait">
          {searchStep === 'upload' && (
            <motion.div
              key="upload-step"
              initial="initial"
              animate="animate"
              exit="exit"
              variants={pageVariants}
              className="container max-w-[90rem] mx-auto px-4"
            >
              {/* Built with heart in Miami Beach - above title with white background */}
              <motion.div
                className="text-center mb-4"
                animate={{
                  opacity: backgroundFocus ? 0 : 1,
                  y: backgroundFocus ? -20 : 0
                }}
                transition={{
                  duration: 0.3
                }}
              >
              {/* Built with heart in Miami Beach - above title with white background */}
              <div className="text-center mt-4">
                <div className="text-sm text-blue-600 dark:text-blue-400 px-4 py-1.5 rounded-full font-medium bg-white dark:bg-gray-800 border border-blue-100 dark:border-blue-800/40 shadow-sm inline-flex items-center gap-1 mx-auto">
                  <span className="font-bold">Built with</span>
                  <span className="text-blue-500 transform scale-110 inline-block">♥</span>
                  <span className="bg-gradient-to-r from-blue-500 to-blue-600 bg-clip-text text-transparent font-semibold">in Miami Beach</span>
                </div>
              </div>
              </motion.div>

              {/* Title with smooth transition */}
              <motion.div
                className="max-w-4xl mx-auto text-center mb-3 md:mb-4 transition-opacity duration-500 ease-in-out"
                initial="hidden"
                animate="visible"
                variants={containerVariants}
                style={{
                  opacity: backgroundFocus ? 0 : 1,
                  pointerEvents: backgroundFocus ? 'none' : 'auto'
                } as any}
              >
                <motion.h1
                  variants={itemVariants}
                  className="text-2xl sm:text-3xl md:text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-[#5B7DF4] to-[#3D40CC] dark:from-[#7590FF] dark:to-[#5158f6] font-[RF_Dewi] tracking-tight leading-none mx-auto max-w-none whitespace-normal md:whitespace-nowrap mt-8 mb-8"
                  style={{ fontFamily: "'RF Dewi', sans-serif" } as any}
                >
                  Find Where Faces Appear Across The Web
                </motion.h1>
                <motion.p
                  variants={itemVariants}
                  className="text-[#5B7DF4] dark:text-[#7590FF] text-lg mb-8 font-semibold"
                >

                </motion.p>
              </motion.div>

              {/* Sign in status */}
              <motion.div
                className="max-w-4xl mx-auto text-center mb-6"
                animate={{
                  opacity: backgroundFocus ? 0 : 1,
                  height: backgroundFocus ? 0 : "auto",
                  // marginBottom removed from animate as it's not directly animatable this way
                }}
                style={{ marginBottom: backgroundFocus ? 0 : "1.5rem" } as any} // Apply marginBottom as a style
                transition={{
                  duration: 0.3,
                }}
              >

              </motion.div>

              {/* Main card containing upload area and footer */}
              <motion.div
                variants={itemVariants}
                animate={{
                  y: backgroundFocus ? -100 : 0,
                }}
                // Removed boxShadow from style prop here due to TS error hint
                // Relying on className="shadow-xl" and potential whileHover boxShadow
                transition={{
                  duration: 0.3,
                }}
                className={`bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden max-w-7xl mx-auto border-0 relative ${
                  backgroundFocus ? 'ring-2 ring-[#92A5FF]/50 dark:ring-[#FFA1BF]/50' : ''
                }`}
                whileHover={{ boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.15)" } as any} // whileHover can accept boxShadow
              >
                {/* Replace the blue gradient header with new design */}
                {/* Card header with original gradient background */}
                <div className="flex justify-between items-center mb-4 bg-gradient-to-r from-[#92A5FF] to-[#5158f6] px-6 py-3 shadow-sm text-white">
                  {/* Left Side: FaceTrace icon with face count */}
                  <div className="flex items-center">
                    <div className="h-7 w-7 mr-2">
                      <svg className="w-full h-full" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
                        {/* Face outline - geometric with bolder stroke filling entire space */}
                        <polygon
                          points="50,10 25,25 15,50 25,75 50,90 75,75 85,50 75,25"
                          stroke="white"
                          strokeWidth="4"
                          strokeOpacity="1"
                          fill="none"
                          strokeLinejoin="round"
                        />

                        {/* Biometric identification grid */}
                        <path d="M50,10 L50,90" stroke="white" strokeWidth="0.9" strokeOpacity="0.5" strokeDasharray="3 2" />
                        <path d="M15,50 L85,50" stroke="white" strokeWidth="0.9" strokeOpacity="0.5" strokeDasharray="3 2" />
                        <path d="M25,25 L75,75" stroke="white" strokeWidth="0.9" strokeOpacity="0.5" strokeDasharray="3 2" />
                        <path d="M25,75 L75,25" stroke="white" strokeWidth="0.9" strokeOpacity="0.5" strokeDasharray="3 2" />

                        {/* Eyes - geometric with thicker stroke */}
                        <rect x="32" y="40" width="8" height="7" rx="1" stroke="white" strokeWidth="2.5" strokeOpacity="1" fill="none" />
                        <rect x="60" y="40" width="8" height="7" rx="1" stroke="white" strokeWidth="2.5" strokeOpacity="1" fill="none" />

                        {/* Mouth - straight line for serious expression */}
                        <path d="M35,65 L65,65" stroke="white" strokeWidth="2" strokeOpacity="0.9" fill="none" strokeLinecap="round" />
                      </svg>
                    </div>
                    {/* Display faces count or loading/timeout state */}
                    <span className="font-semibold">
                      {facesCount !== undefined ? (
                        <>{facesCount.toLocaleString()} <span className="text-white/90">Faces</span></>
                      ) : facesCountStatus === 'loading' ? (
                        <>
                          <span className="inline-block animate-pulse">
                            Loading<span className="animate-[blink_1s_infinite]">...</span>
                          </span>
                        </>
                      ) : (
                        <>1B+ <span className="text-white/90">Faces</span></>
                      )}
                    </span>
                  </div>

                  {/* Center Area - User/Guest Mode with pricing */}
                  <div className="text-center hidden sm:block">
         
                  </div>

                  {/* Right Side: Credits/Tokens display - TEMPORARY DISABLE: Show when auth enabled and signed in */}
                  <div className="flex items-center">
                    {!isAuthDisabled && isSignedIn && (
                      <div className="text-right">
                        {isTokenLoading ? (
                          <span className="font-bold bg-green-500/30 px-2 py-0.5 rounded-full flex items-center">
                            <svg className="animate-spin mr-1 h-3 w-3 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Loading...
                          </span>
                        ) : (
                          <span
                            className="font-bold px-2 py-0.5 rounded-full cursor-help"
                            title="Your available tokens for searches"
                            style={{
                              background: remainingTokens > 0 ? 'rgba(34, 197, 94, 0.3)' : 'rgba(239, 68, 68, 0.3)',
                              color: remainingTokens > 0 ? 'white' : 'rgba(255, 255, 255, 0.9)'
                            }}
                          >
                            {remainingTokens} Free {remainingTokens === 1 ? 'Token' : 'Tokens'}
                          </span>
                        )}
                      </div>
                    )}
                  </div>
                </div>

                {/* Full width upload area - 80% height */}
                <label
                  htmlFor={backgroundFocus ? undefined : "file-upload"}
                  className={`p-8 sm:p-12 cursor-default block transition-all duration-300 relative flex-col justify-center
                    ${dragActive ? 'bg-blue-50/80 dark:bg-blue-900/20' : 'hover:bg-blue-50/50 dark:hover:bg-gray-700/50'}`}
                  onDragEnter={!backgroundFocus ? handleDragEnter : undefined}
                  onDragLeave={!backgroundFocus ? handleDragLeave : undefined}
                  onDragOver={!backgroundFocus ? handleDragOver : undefined}
                  onDrop={!backgroundFocus ? handleDrop : undefined}
                >
                  <input
                    type="file"
                    id="file-upload"
                    className="sr-only"
                    accept="image/*,.heic"
                    multiple
                    onChange={handleFileInputChange}
                  />

                  {!selectedFiles.length ? (
                    // Empty state
                    <div className="text-center h-full flex flex-col items-center justify-center">
                      <div className="mb-4 bg-gradient-to-br from-blue-50 to-white dark:from-gray-700 dark:to-gray-800 p-6 rounded-xl inline-flex justify-center mx-auto border-2 border-dashed border-blue-200 dark:border-gray-600 group-hover:border-[#92A5FF] dark:group-hover:border-[#92A5FF] transition-colors relative overflow-hidden shadow-inner">
                        <div className="absolute inset-0 bg-grid-pattern opacity-10 pointer-events-none"></div>
                        <svg className="w-24 h-24 text-[#92A5FF] group-hover:scale-105 transition-transform duration-300" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
                          {/* Corner brackets - enhanced with thicker lines and better proportions */}
                          <path d="M10 10 L10 30 M10 10 L30 10" stroke="currentColor" strokeWidth="2.5" strokeOpacity="1" strokeLinecap="square" />
                          <path d="M90 10 L90 30 M90 10 L70 10" stroke="currentColor" strokeWidth="2.5" strokeOpacity="1" strokeLinecap="square" />
                          <path d="M10 90 L10 70 M10 90 L30 90" stroke="currentColor" strokeWidth="2.5" strokeOpacity="1" strokeLinecap="square" />
                          <path d="M90 90 L90 70 M90 90 L70 90" stroke="currentColor" strokeWidth="2.5" strokeOpacity="1" strokeLinecap="square" />

                          {/* Grid lines to enhance the tech/geometric appearance */}
                          <path d="M10 50 L25 50 M75 50 L90 50" stroke="currentColor" strokeWidth="0.8" strokeOpacity="0.6" strokeDasharray="2 2" />
                          <path d="M50 10 L50 25 M50 75 L50 90" stroke="currentColor" strokeWidth="0.8" strokeOpacity="0.6" strokeDasharray="2 2" />

                          {/* Main face outline - geometric */}
                          <polygon
                            points="50,20 30,35 25,50 30,65 50,80 70,65 75,50 70,35"
                            stroke="currentColor"
                            strokeWidth="2.5"
                            strokeOpacity="0.9"
                            fill="none"
                            strokeLinejoin="round"
                            className="group-hover:stroke-[#FFA1BF] transition-colors duration-500"
                          />

                          {/* Triangulation mesh lines */}
                          <path d="M50,20 L50,80" stroke="currentColor" strokeWidth="1" strokeOpacity="0.7" />
                          <path d="M30,35 L70,35" stroke="currentColor" strokeWidth="1" strokeOpacity="0.7" />
                          <path d="M25,50 L75,50" stroke="currentColor" strokeWidth="1" strokeOpacity="0.7" />
                          <path d="M30,65 L70,65" stroke="currentColor" strokeWidth="1" strokeOpacity="0.7" />
                          <path d="M30,35 L70,65" stroke="currentColor" strokeWidth="0.8" strokeOpacity="0.5" />
                          <path d="M70,35 L30,65" stroke="currentColor" strokeWidth="0.8" strokeOpacity="0.5" />

                          {/* Eyes - geometric */}
                          <rect x="35" y="40" width="6" height="6" rx="1" stroke="currentColor" strokeWidth="1.5" strokeOpacity="0.9" fill="none" />
                          <rect x="59" y="40" width="6" height="6" rx="1" stroke="currentColor" strokeWidth="1.5" strokeOpacity="0.9" fill="none" />

                          {/* Mouth - geometric */}
                          <path d="M40,65 L50,60 L60,65" stroke="currentColor" strokeWidth="1.2" strokeOpacity="0.8" fill="none" />

                          {/* Intersection points with small dots */}
                          <circle cx="50" cy="20" r="1" fill="currentColor" fillOpacity="0.8" />
                          <circle cx="30" cy="35" r="1" fill="currentColor" fillOpacity="0.8" />
                          <circle cx="25" cy="50" r="1" fill="currentColor" fillOpacity="0.8" />
                          <circle cx="30" cy="65" r="1" fill="currentColor" fillOpacity="0.8" />
                          <circle cx="50" cy="80" r="1" fill="currentColor" fillOpacity="0.8" />
                          <circle cx="70" cy="65" r="1" fill="currentColor" fillOpacity="0.8" />
                          <circle cx="75" cy="50" r="1" fill="currentColor" fillOpacity="0.8" />
                          <circle cx="70" cy="35" r="1" fill="currentColor" fillOpacity="0.8" />

                          {/* Upload indicator integrated with scanning effect */}
                          <g className="group-hover:opacity-100 opacity-80 transition-opacity duration-300">
                            <path d="M50,25 L50,45" stroke="currentColor" strokeWidth="1.8" strokeOpacity="0.9" className="group-hover:stroke-[#FFA1BF] transition-colors duration-500" />
                            <polygon
                              points="44,31 50,25 56,31"
                              stroke="currentColor"
                              strokeWidth="1.8"
                              strokeOpacity="0.9"
                              fill="none"
                              strokeLinejoin="round"
                              className="group-hover:stroke-[#FFA1BF] transition-colors duration-500"
                            />
                            <text x="38" y="94" className="font-['Electrolize','SF_Mono',monospace] uppercase tracking-wide" fontSize="6" fill="currentColor" opacity="0.7">Upload</text>
                          </g>

                          {/* Scanner effect */}
                          <motion.path
                            d="M30 50 L70 50"
                            stroke="currentColor"
                            strokeWidth="0.8"
                            strokeOpacity="0.6"
                            initial={{ opacity: 0.3 }}
                            animate={{
                              y: [0, 10, 0, -10, 0],
                              opacity: [0.3, 0.7, 0.3]
                            }}
                            transition={{
                              duration: 3,
                              repeat: Infinity,
                              repeatType: "loop"
                            }}
                          />
                        </svg>
                      </div>

                      <h2 className="text-2xl sm:text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-[#92A5FF] to-[#FFA1BF] mb-4 tracking-tight">
                      Drag & Drop to Upload Photos
                      </h2>

                      <p className="text-gray-600 dark:text-gray-300 mb-4 text-base bg-gray-50 dark:bg-gray-800/60 py-2 px-4 rounded-lg shadow-sm inline-block">
                        JPEG, PNG, WEBP, BMP, HEIC (max 50MB each)
                      </p>


                    </div>
                  ) : (
                    // Selected files state
                    <div>
                      <div className="mb-0">


                        {/* Mobile: Horizontal Slider - Improved UI */}
                        <div className="sm:hidden w-full overflow-x-auto pb-4 snap-x snap-mandatory scrollbar-hide">
                          <div className="flex space-x-3 px-4"> {/* Increased horizontal padding */}
                            {imagePreviews.map((preview, index) => (
                              <div key={index} className="flex-shrink-0 w-48 snap-center"> {/* Adjusted width */}
                                <div className="relative rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700 shadow-md group h-36 z-10"> {/* Added z-10 to keep above spotlight */}
                                  {/* Image Preview with improved visibility */}
                                  <img
                                    src={preview}
                                    alt={`Preview ${index + 1}`}
                                    className="w-full h-full object-cover relative z-10" // Added relative z-10
                                  />
                                  {/* Info overlay at the top */}
                                  <div className="absolute top-0 left-0 right-0 p-1.5 bg-gradient-to-b from-black/60 to-transparent z-10 flex justify-between items-start">
                                    {/* Left side: Count */}
                                    <span className="text-white text-xs font-medium">{index + 1} of 3</span>
                                    {/* Right side: File type and Remove button */}
                                    <div className="flex items-center space-x-1">
                                      <span className="uppercase bg-white/20 px-1.5 py-0.5 rounded text-[10px] text-white">
                                        {selectedFiles[index]?.name.split('.').pop()}
                                      </span>
                                      {/* Remove button - Adjusted position */}
                                      <button
                                        onClick={(e) => {
                                          e.preventDefault();
                                          const newFiles = [...selectedFiles];
                                      newFiles.splice(index, 1);
                                      setSelectedFiles(newFiles);

                                      const newPreviews = [...imagePreviews];
                                      URL.revokeObjectURL(newPreviews[index]);
                                      newPreviews.splice(index, 1);
                                      setImagePreviews(newPreviews);

                                      if (newFiles.length === 0) {
                                        setBackgroundFocus(false);
                                          }
                                        }}
                                        className="bg-red-500 text-white rounded-full p-1 opacity-100 transition-opacity shadow-md z-20"
                                        aria-label="Remove image"
                                      >
                                        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" /></svg>
                                      </button>
                                    </div>
                                  </div>
                                </div>
                                {/* File name below card */}
                                <div className="mt-1.5 text-xs">
                                  <p className="truncate text-gray-700 dark:text-gray-300 font-medium">{selectedFiles[index]?.name}</p>
                                </div>
                              </div>
                            ))}
                            {/* Add more button in slider - Improved UI */}
                            {selectedFiles.length < 3 && (
                              <div className="flex-shrink-0 w-48 snap-center"> {/* Adjusted width */}
                                <label htmlFor="add-more-upload-mobile" className="h-36 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600 hover:border-[#92A5FF] hover:bg-[#92A5FF]/5 flex items-center justify-center cursor-pointer transition-all duration-300 bg-gray-50 dark:bg-gray-800/50">
                                  <input type="file" id="add-more-upload-mobile" className="sr-only" accept="image/*,.heic" multiple onChange={handleFileInputChange}/>
                                  <div className="text-center">
                                    <div className="w-10 h-10 bg-[#92A5FF]/15 rounded-full flex items-center justify-center mx-auto mb-2"><svg className="w-6 h-6 text-[#92A5FF]" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" /></svg></div>
                                    <span className="text-sm text-[#92A5FF] font-semibold block mb-1">Add Image</span>
                                    <p className="text-xs text-gray-500 dark:text-gray-400">{3 - selectedFiles.length} more</p>
                                  </div>
                                </label>
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Desktop: Grid Layout */}
                        <div className="hidden sm:block bg-gradient-to-r from-gray-50 to-white dark:from-gray-800/80 dark:to-gray-800/60 border border-gray-200 dark:border-gray-700 rounded-lg p-5 shadow-inner">
                          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-3 gap-4 mb-4">
                            {imagePreviews.map((preview, index) => (
                              <div key={index} className="aspect-video rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700 relative group shadow-lg transform transition-transform hover:scale-[1.02]">
                                {/* Blurred background */}
                                <div className="absolute inset-0 w-auto overflow-hidden">
                                  <img
                                    src={preview}
                                    alt=""
                                    className="w-auto min-w-full min-h-full object-cover blur-xl opacity-50 scale-110"
                                  />
                                </div>

                                {/* Main image preview - Added back */}
                                <img
                                  src={preview}
                                  alt={`Preview ${index + 1}`}
                                  className="absolute inset-0 w-full h-full object-contain z-0" // Use object-contain and ensure it's below overlays (z-0)
                                />

                                {/* Card header with image count and file type */}
                                <div className="absolute top-0 left-0 right-0 bg-gradient-to-r from-[#92A5FF]/90 to-[#5158f6]/90 text-white py-2 px-3 text-sm font-bold z-10 backdrop-blur-sm flex justify-between items-center shadow-md">
                                  <span>{index + 1} of {imagePreviews.length} Images</span>
                                  <span className="uppercase flex items-center">
                                    {selectedFiles[index]?.name.split('.').pop()}
                                    {isCompressing && <span className="ml-1" title="Compressed">🗜️</span>}
                                  </span>
                                </div>

                                <button
                                  onClick={(e) => {
                                    e.preventDefault();
                                    const newFiles = [...selectedFiles];
                                    newFiles.splice(index, 1);
                                    setSelectedFiles(newFiles);

                                    const newPreviews = [...imagePreviews];
                                    URL.revokeObjectURL(newPreviews[index]);
                                    newPreviews.splice(index, 1);
                                    setImagePreviews(newPreviews);

                                    // If removing the last image, reset the spotlight
                                    if (newFiles.length === 0) {
                                      setBackgroundFocus(false);
                                    }
                                  }}
                                  className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1.5 opacity-0 group-hover:opacity-100 transition-opacity shadow-lg z-20"
                                  aria-label="Remove image"
                                >
                                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                  </svg>
                                </button>

                                {/* Image Selection Overlay */}
                                <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent py-4 px-3 text-white text-xs font-medium opacity-0 group-hover:opacity-100 transition-opacity">
                                  Click to remove this image
                                </div>
                              </div>
                            ))}

                            {selectedFiles.length < 3 && (
                              <label
                                htmlFor="add-more-upload"
                                className="aspect-video rounded-lg border-2 border-dashed border-gray-200 dark:border-gray-700 hover:border-[#92A5FF] hover:bg-[#92A5FF]/5 flex items-center justify-center cursor-pointer transition-all duration-300"
                              >
                                <input
                                  type="file"
                                  id="add-more-upload"
                                  className="sr-only"
                                  accept="image/*,.heic"
                                  multiple
                                  onChange={handleFileInputChange}
                                />
                                <div className="text-center">
                                  <div className="w-12 h-12 bg-[#92A5FF]/15 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <svg className="w-7 h-7 text-[#92A5FF]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                    </svg>
                                  </div>
                                  <span className="text-sm sm:text-base text-[#92A5FF] font-bold block mb-1">Add more</span>
                                  <p className="text-xs text-blue-600 dark:text-blue-300 px-3 py-0.5 rounded-full bg-blue-50 dark:bg-blue-900/20">
                                    {3 - selectedFiles.length} more allowed
                                  </p>
                                </div>
                              </label>
                            )}
                          </div>
                        </div>
                      </div>

                      <div className="flex flex-col items-center space-y-4 mt-6">
                        {/* Add email input field for guest users */}
                        {authInitialized && !isSignedIn && (
                          <div className="w-full max-w-md">
                            <input
                              type="email"
                              value={guestEmail}
                              onChange={(e) => {
                                setGuestEmail(e.target.value);
                                // Simple email validation regex
                                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                                setIsEmailValid(emailRegex.test(e.target.value));
                                // Clear error when user types
                                if (error) setError(null);
                              }}
                              placeholder="Enter your email address"
                              className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-[#92A5FF] transition-all duration-300"
                              required
                            />
                            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 ml-1">
                              Required for guest searches. We'll email you when results are ready.
                            </p>
                          </div>
                        )}

                        <div className="flex justify-center space-x-4 w-full max-w-md">
                          {/* Single button to start the search process */}
                          <motion.button
                            onClick={(e) => {
                              e.preventDefault();
                              // Directly call handleSearch - backend will create a private report
                              // The results page will handle unlocking
                              handleSearch(); 
                            }}
                            disabled={!selectedFiles.length || isCheckingOut || isProcessingPayment || 
                                     (authInitialized && !isSignedIn && !isEmailValid)}
                            className={`relative z-10 px-8 py-3 rounded-full text-white transition-all duration-300 text-lg font-bold shadow-xl flex-1 ${
                              !selectedFiles.length || (authInitialized && !isSignedIn && !isEmailValid)
                                ? 'bg-gray-400 cursor-not-allowed opacity-70'
                                : isCheckingOut || isProcessingPayment
                                ? 'bg-gradient-to-r from-blue-500 to-blue-600 cursor-wait'
                                : 'bg-gradient-to-r from-[#92A5FF] to-[#5158f6] hover:shadow-lg'
                            }`}
                            whileHover={{
                              scale: selectedFiles.length && !isCheckingOut && !isProcessingPayment ? 1.03 : 1,
                              boxShadow: selectedFiles.length && !isCheckingOut && !isProcessingPayment ? "0 4px 20px -3px rgba(146, 165, 255, 0.5)" : "none",
                            } as any}
                            transition={{
                              type: "spring",
                              stiffness: 400,
                              damping: 15
                            }}
                          >
                            <div className="flex items-center justify-center">
                              {isProcessingPayment ? (
                                <>
                                  <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                  </svg>
                                  Processing Payment...
                                </>
                              ) : isCheckingOut ? (
                                <>
                                  <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                  </svg>
                                  Preparing Checkout...
                                </>
                              ) : !selectedFiles.length ? (
                                "Upload Photos to Search"
                              ) : isSignedIn ? (
                                <>
                                  {isNewUser ? "Run FaceTrace" : "Run FaceTrace"}
                                </>
                              ) : (
                                <>
                                  Run FaceTrace
                                </>
                              )}

                              {/* Moving scanner effect - only shown when button is active */}
                              {selectedFiles.length > 0 && !isCheckingOut && !isProcessingPayment && (
                                <motion.div
                                  className="absolute bottom-0 left-0 right-0 h-0.5 bg-white/30"
                                  initial={{ left: 0, width: 0 } as any}
                                  animate={{
                                    left: ['0%', '100%', '0%'],
                                    width: ['0%', '50%', '0%']
                                  } as any}
                                  transition={{
                                    duration: 5,
                                    repeat: Infinity,
                                    ease: "linear"
                                  }}
                                />
                              )}
                            </div>
                          </motion.button>
                        </div>
                      </div>

                      {/* Join FaceTrace section - COMPLETELY REMOVED */}
                    </div>
                  )}

                  {error && (
                    <div className="mt-4 text-center text-red-500 text-sm bg-red-50 dark:bg-red-900/20 p-3 rounded-lg">
                      {error}
                    </div>
                  )}
                </label>

                {/* Card footer with search types and guarantee - redesigned - 20% height */}
                <motion.div
                  className="border-t border-gray-100 dark:border-gray-700 bg-gradient-to-b from-white to-blue-50 dark:from-gray-800 dark:to-gray-800/80 p-2 sm:p-3"
                  animate={{
                    height: backgroundFocus ? "auto" : "auto",
                    opacity: backgroundFocus ? 0.8 : 1,
                  }}
                  style={{
                    padding: backgroundFocus ? "0.5rem" : "0.75rem"
                  } as any}
                  transition={{
                    duration: 0.5,
                    ease: "easeInOut"
                  }}
                >
                  <motion.div
                    className="text-center mb-3"
                  animate={{
                    opacity: backgroundFocus ? 0 : 1,
                    height: backgroundFocus ? 0 : "auto",
                    overflow: "hidden"
                  }}
                  style={{ marginBottom: backgroundFocus ? 0 : "0.75rem" } as any}
                  >
                    <a
                      href="#upload-section"
                      className="inline-flex items-center px-3 py-1.5 text-sm font-bold hover:scale-105 active:scale-95 transition-all duration-300 cursor-pointer"
                    >
                      <span className="text-[#5B7DF4] dark:text-[#7590FF] text-sm font-semibold">Discover, Monitor, and Protect Your Digital Footprint</span>
                    </a>
                  </motion.div>

                  {/* Marquee container for pills */}
                  <div className="w-full relative group social-pills-container"> {/* Removed overflow-hidden */}
                    {/* Inner container for social media icons */}
                    <motion.div
                      className="flex flex-nowrap gap-3 justify-center social-pills-wrapper"
                      initial={{ scale: 1, opacity: 1 }}
                      animate={{
                        scale: backgroundFocus ? 0.9 : 1,
                        opacity: backgroundFocus ? 0.7 : 1
                      }}
                    >
                      {/* Social Media Pills with Sequential Animation */}
                      {[
                        { name: "Instagram", bg: "bg-blue-500", icon: <FaInstagram className="w-5 h-5 text-white" /> },
                        { name: "LinkedIn", bg: "bg-blue-600", icon: <FaLinkedin className="w-5 h-5 text-white" /> },
                        { name: "OnlyFans", bg: "bg-blue-500", icon: <SiOnlyfans className="w-5 h-5 text-white" /> },
                        { name: "Facebook", bg: "bg-blue-700", icon: <FaFacebook className="w-5 h-5 text-white" /> },
                        { name: "Twitter", bg: "bg-black", icon: <FaTwitter className="w-5 h-5 text-white" /> },
                        { name: "TikTok", bg: "bg-black", icon: <FaTiktok className="w-5 h-5 text-white" /> },
                        { name: "YouTube", bg: "bg-red-600", icon: <FaYoutube className="w-5 h-5 text-white" /> },
                        { name: "Reddit", bg: "bg-orange-600", icon: <FaReddit className="w-5 h-5 text-white" /> },
                        { name: "Twitch", bg: "bg-purple-700", icon: <FaTwitch className="w-5 h-5 text-white" /> },
                        { name: "Pinterest", bg: "bg-red-700", icon: <FaPinterest className="w-5 h-5 text-white" /> },
                        { name: "Pornhub", bg: "bg-black", icon: <svg className="w-5 h-5 text-[#FF9000]" viewBox="0 0 24 24" fill="currentColor"><path d="M21.8 12.5c0 .3-.1.5-.4.5h-2.8c-.4 0-.7.4-.6.8 0 .2.3.4.5.4h2.8c.4 0 .7-.3.7-.7v-.2c.1-.4-.1-.8-.2-.8zm-9.7.9c-.4 0-.7.4-.6.8 0 .2.3.4.5.4h2.1c.4 0 .7-.4.6-.8 0-.2-.3-.4-.5-.4h-2.1zm-3.2 0c-.2 0-.5.2-.5.5v3.2c0 .3.2.5.5.5h2.1c.4 0 .7-.4.6-.8 0-.2-.3-.4-.5-.4H9.3v-3h-.4zm5.2-3.2c-.2 0-.5.2-.5.5v6.4c0 .3.2.5.5.5s.5-.2.5-.5v-6.4c0-.3-.2-.5-.5-.5zm11.8.1h-3.3c-.3 0-.5.2-.5.5v6.3c0 .2.1.3.2.4.1.1.3.1.4.1.2 0 .3-.1.4-.1.1-.1.2-.2.2-.4v-2.5h2c.4 0 .7-.4.6-.8 0-.2-.3-.4-.5-.4h-2v-2h2.5c.4 0 .7-.4.6-.8-.1-.2-.3-.3-.6-.3zM1.1 9.8H0C0 10.3 0 16 0 16.3c0 .4.4.7.8.6.2 0 .4-.3.4-.5v-2.5h.3c1.6 0 2.9-1.3 2.9-2.9s-1.3-3-3.3-3.2zm.4 4c-.1.1-.3.1-.4.1h-.4v-3h.4c.1 0 .3 0 .4.1.5.2.8.7.8 1.4 0 .6-.3 1.1-.8 1.4zm17.9-4h-2.1c-.3 0-.5.2-.5.5v6.4c0 .3.2.5.5.5h2.1c1.7 0 3.2-1.6 3.2-3.7s-1.6-3.7-3.2-3.7zm0 6.3h-1.5v-5.3h1.5c1.2 0 2.2 1.2 2.1 2.6 0 1.5-1 2.7-2.1 2.7C7.5 10.8 8.5 12 8.5 13.5c0 1.4-1 2.6-2.2 2.6zM6.3 9.8c-1.7 0-3.2 1.6-3.2 3.7s1.6 3.7 3.2 3.7c1.7 0 3.2-1.6 3.2-3.7s-1.4-3.7-3.2-3.7zm0 6.3c-1.2 0-2.1-1.2-2.1-2.6 0-1.5 1-2.7 2.1-2.7C7.5 10.8 8.5 12 8.5 13.5c0 1.4-1 2.6-2.2 2.6z"/></svg> },
                        { name: "PacerMonitor", bg: "bg-blue-800", icon: <svg className="w-5 h-5 text-white" viewBox="0 0 24 24" fill="currentColor"><path d="M20.5 3h-17A2.5 2.5 0 001 5.5v13A2.5 2.5 0 003.5 21h17a2.5 2.5 0 002.5-2.5v-13A2.5 2.5 0 0020.5 3zm-7 13h-8a.5.5 0 010-1h8a.5.5 0 010 1zm7-4h-15a.5.5 0 010-1h15a.5.5 0 010 1zm0-4h-15a.5.5 0 010-1h15a.5.5 0 010 1z"/></svg> },
                        { name: "Mugshots", bg: "bg-red-800", icon: <svg className="w-5 h-5 text-white" viewBox="0 0 24 24" fill="currentColor"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 3c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm0 14.2c-2.5 0-4.71-1.28-6-3.22.03-1.99 4-3.08 6-3.08 1.99 0 5.97 1.09 6 3.08-1.29 1.94-3.5 3.22-6 3.22z"/></svg> },
                        { name: "and more...", bg: "bg-gray-600", icon: <FaGlobe className="w-5 h-5 text-white" /> }
                      ].map((platform, index) => (
                        <div
                          key={platform.name}
                          data-social-icon={index}
                          className={`${platform.bg} rounded-md shadow-sm hover:shadow hover:scale-105 transition-all duration-300 cursor-default flex items-center justify-start h-8 overflow-hidden w-8 mx-1`}
                        >
                          <div className="flex items-center px-2 min-w-[32px] w-full">
                            <div className="flex-shrink-0">
                              {platform.icon}
                            </div>
                            <span className="text-white text-sm font-medium ml-2 whitespace-nowrap overflow-hidden">{platform.name}</span>
                          </div>
                        </div>
                      ))}
                    </motion.div>
                  </div>
                </motion.div>
              </motion.div>
            </motion.div>
          )}
          {searchStep === 'processing' && (
            <motion.div
              key="processing-step"
              initial="initial"
              animate="animate"
              exit="exit"
              variants={pageVariants}
              className="container max-w-7xl mx-auto px-4"
              onAnimationComplete={() => setBackgroundFocus(true)}
            >
              <div className="max-w-xl mx-auto bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 text-center relative overflow-hidden border border-blue-200 dark:border-blue-900">
                {/* Processing step - improved for mobile */}
                <motion.div
                  className="absolute left-0 right-0 h-0.5 bg-[#00FFFF]/70 z-10"
                  initial={{ top: 0, opacity: 0 } as any}
                  animate={{
                    top: ['0%', '100%', '0%'], // top is valid in animate for keyframes
                    opacity: [0.4, 1, 0.4]
                  } as any}
                  transition={{
                    duration: 2.5,
                    repeat: Infinity,
                    ease: "linear"
                  }}
                >
                  <div className="absolute top-0 left-1/2 w-40 h-full bg-[#00FFFF]/40 blur-sm -translate-x-1/2"></div>
                </motion.div>

                {/* Hexagon grid background */}
                <div className="absolute inset-0 opacity-5 pointer-events-none"
                  style={{
                    backgroundImage: `url("data:image/svg+xml,%3Csvg width='24' height='40' viewBox='0 0 24 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0,0 L24,0 L24,40 L0,40 L0,0 Z M12,6.928 L4,12 L4,24 L12,29.072 L20,24 L20,12 L12,6.928 Z' stroke='%2392A5FF' stroke-width='1' fill='none'/%3E%3C/svg%3E")`,
                    backgroundSize: '24px 40px'
                  }}
                />

                <div className="mb-8 relative z-10">
                  <div className="inline-flex items-center justify-center w-16 h-16 sm:w-20 sm:h-20 bg-blue-50 dark:bg-gray-700 rounded-full mb-4 sm:mb-6 relative">
                    {searchProgress === 0 ? (
                      <svg className="w-8 h-8 sm:w-10 sm:h-10 text-[#92A5FF]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    ) : (
                      <svg className="w-8 h-8 sm:w-10 sm:h-10 text-[#92A5FF]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                      </svg>
                    )}

                    {/* Rotating biometric circle */}
                    <motion.div
                      className="absolute inset-0 rounded-full border-2 border-blue-300 dark:border-blue-600 border-dashed"
                      animate={{ rotate: 360 }}
                      transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                    />
                  </div>

                  <h2 className="text-xl sm:text-2xl font-bold text-gray-800 dark:text-white mb-2">
                    {isUploading ? 'Uploading Photos...' :
                     searchProgress === 0 ? responseMessage :
                     'Searching Across The Web...'}
                  </h2>

                  <p className="text-sm sm:text-base text-gray-500 dark:text-gray-400 mb-6 sm:mb-8 px-4">
                    {isUploading ? 'Preparing your images for processing...' :
                     searchProgress === 0 ? 'Your search is in the queue.' :
                     'This may take a minute. We\'re searching millions of images.'}
                  </p>
                </div>

                {/* Image previews with biometric scanning effect */}
                {imagePreviews.length > 0 && (
                  <div className="mb-8 relative">
                    <div className="flex justify-center gap-4">
                      {imagePreviews.slice(0, 3).map((preview, index) => (
                        <div key={index} className="w-16 h-16 rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700 relative">
                          <img
                            src={preview}
                            alt={`Preview ${index + 1}`}
                            className="w-full h-full object-cover"
                          />
                          {/* Scanning effect overlay */}
                          <motion.div
                            className="absolute inset-0 bg-gradient-to-b from-transparent via-[#00FFFF]/20 to-transparent"
                            initial={{ top: '-100%' } as any} // top is valid in initial
                            animate={{ top: ['0%', '100%', '0%'] } as any} // top is valid in animate for keyframes
                            transition={{ duration: 2, repeat: Infinity, ease: "linear", delay: index * 0.3 }}
                          />
                          {/* Corner brackets */}
                          <div className="absolute top-0 left-0 w-3 h-3 border-t border-l border-[#00FFFF]/70"></div>
                          <div className="absolute top-0 right-0 w-3 h-3 border-t border-r border-[#00FFFF]/70"></div>
                          <div className="absolute bottom-0 left-0 w-3 h-3 border-b border-l border-[#00FFFF]/70"></div>
                          <div className="absolute bottom-0 right-0 w-3 h-3 border-b border-r border-[#00FFFF]/70"></div>
                        </div>
                      ))}
                      {imagePreviews.length > 3 && (
                        <div className="w-16 h-16 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center text-gray-500 dark:text-gray-400 relative">
                          +{imagePreviews.length - 3}
                          {/* Corner brackets */}
                          <div className="absolute top-0 left-0 w-3 h-3 border-t border-l border-[#00FFFF]/70"></div>
                          <div className="absolute top-0 right-0 w-3 h-3 border-t border-r border-[#00FFFF]/70"></div>
                          <div className="absolute bottom-0 left-0 w-3 h-3 border-b border-l border-[#00FFFF]/70"></div>
                          <div className="absolute bottom-0 right-0 w-3 h-3 border-b border-r border-[#00FFFF]/70"></div>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Enhanced progress bar with glowing effect */}
                <div className="mb-4 relative z-10">
                  <div className="h-2 bg-gray-100 dark:bg-gray-700 rounded-full overflow-hidden">
                    <motion.div
                      className="h-full bg-gradient-to-r from-[#92A5FF] to-[#FFA1BF] relative"
                      style={{ width: `${searchProgress}%` } as any} // width should be in style
                    >
                      {/* Glowing edge effect */}
                      <div className="absolute right-0 top-0 bottom-0 w-4 bg-white/30 blur-sm"></div>
                    </motion.div>
                  </div>

                  <div className="flex justify-between items-center mt-2">
                    <div className="text-sm text-[#92A5FF] font-['Electrolize','SF_Mono',monospace] tracking-wide">
                      {searchProgress === 0 ? 'WAITING' : 'PROCESSING'}
                    </div>
                    <div className="text-sm text-[#92A5FF] font-['Electrolize','SF_Mono',monospace] tracking-wide">
                      {searchProgress}%
                    </div>
                  </div>
                </div>

                <div className="text-sm text-gray-500 dark:text-gray-400 relative z-10 font-['Electrolize','SF_Mono',monospace] tracking-wide">
                  <motion.div
                    key={responseMessage}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.5 }}
                  >
                    <span className="text-blue-500">{isUploading ? "UPLOADING" : "SEARCHING"}</span> {responseMessage}
                  </motion.div>
                </div>

                {/* Status and timestamp info instead of technical data */}
                <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700 text-center text-xs font-['Electrolize','SF_Mono',monospace]">
                  <div className="text-gray-500 dark:text-gray-400 mb-1">
                    {new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit', second:'2-digit', hour12: true})}
                  </div>
                  <div className="text-[#92A5FF] flex items-center justify-center">
                    <svg className="animate-pulse w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                    {searchProgress === 0 ? 'QUEUE STATUS: WAITING' : `SEARCH STATUS: ${isUploading ? 'UPLOADING' : 'SEARCHING'}`}
                  </div>
                </div>
              </div>
            </motion.div>
          )}

          {searchStep === 'results' && (
            <motion.div
              key="results-step"
              initial="initial"
              animate="animate"
              exit="exit"
              variants={pageVariants}
              className="container max-w-7xl mx-auto px-4"
              onAnimationStart={() => setBackgroundFocus(true)}
            >
              <div className="mx-auto">
                {/* Combined card with all results content */}
                <div className="bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm rounded-xl shadow-lg overflow-hidden">
                  {/* Report Navbar */}
                  <div className="bg-gradient-to-r from-[#92A5FF] to-[#5158f6] text-white">
                    <div className="container mx-auto px-4 py-3">
                      <div className="flex items-center justify-between flex-wrap gap-3">
                        <div className="flex items-center">
                          <div className="relative h-8 w-8 mr-2">
                            <svg className="w-full h-full" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <polygon
                                points="50,10 25,25 15,50 25,75 50,90 75,75 85,50 75,25"
                                stroke="white"
                                strokeWidth="4"
                                fill="none"
                                strokeLinejoin="round"
                              />
                            </svg>
                          </div>
                          <span className="text-lg font-bold tracking-tight flex items-center">
                            <span className="mr-1">FaceTrace</span>
                            <span className="font-['Electrolize','SF_Mono',monospace]">.Pro</span>
                          </span>
                        </div>

                        <div className="flex items-center gap-2 overflow-x-auto pb-2 sm:pb-0">
                          <button
                            onClick={handleReturnToUpload}
                            className="px-3 py-1 rounded-md bg-white/20 text-white font-medium hover:bg-white/30 transition-colors flex items-center"
                          >
                            <svg className="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                              <path d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                                stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            </svg>
                            New Search
                          </button>

                          <div className="h-6 w-px bg-white/30"></div>

                          <button
                            className="px-3 py-1 rounded-md bg-white/20 text-white font-medium hover:bg-white/30 transition-colors flex items-center"
                            onClick={() => {
                              // Create CSV content
                              const headers = ['ID', 'Title', 'Source Type', 'Confidence', 'URL'].join(',');
                              const csvRows = searchResults.map(result => {
                                return [
                                  result.id,
                                  `"${(result.title || 'Unknown').replace(/"/g, '""')}"`,
                                  extractDomain(result.sourceUrl),
                                  `${Math.round(result.confidence)}%`,
                                  `"${result.sourceUrl}"`
                                ].join(',');
                              });

                              const csvContent = `${headers}\n${csvRows.join('\n')}`;
                              const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                              const url = URL.createObjectURL(blob);
                              const link = document.createElement('a');
                              link.setAttribute('href', url);
                              link.setAttribute('download', `facetrace-export-${new Date().toISOString().slice(0, 10)}.csv`);
                              link.style.visibility = 'hidden';
                              document.body.appendChild(link);
                              link.click();
                              document.body.removeChild(link);
                            }}
                          >
                            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            Export CSV
                          </button>

                          <div className="h-6 w-px bg-white/30"></div>

                          <button
                            className="px-3 py-1 rounded-md bg-white/20 text-white font-medium hover:bg-white/30 transition-colors flex items-center"
                            onClick={() => {
                              // PDF Export functionality
                              alert('PDF export functionality coming soon!');
                            }}
                          >
                            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                            </svg>
                            Export PDF
                          </button>

                          <div className="h-6 w-px bg-white/30"></div>

                          <button
                            className="px-3 py-1 rounded-md bg-white/20 text-white font-medium hover:bg-white/30 transition-colors flex items-center"
                            onClick={() => alert('Removal request feature coming soon!')}
                          >
                            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                            Removal Request
                          </button>

                          <div className="h-6 w-px bg-white/30"></div>

                          <button
                            className="px-3 py-1 rounded-md bg-white/20 text-white font-medium hover:bg-white/30 transition-colors flex items-center"
                            onClick={() => {
                              if (confirm("Are you sure you want to delete this report? This will not delete the underlying search results.")) {
                                // Reset UI but keep search results
                                handleReturnToUpload();
                              }
                            }}
                          >
                            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                            Delete Report
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Top header with image and summary */}
                  <div className="p-4 sm:p-6 border-b border-gray-200 dark:border-gray-700">
                    <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
                      {/* Left column with image */}
                      {imagePreviews.length > 0 && (
                         <div className="w-32 h-32 rounded-lg overflow-hidden border-2 border-blue-500 dark:border-gray-700 shrink-0 shadow-lg mx-auto sm:mx-0">
                           <img
                            src={imagePreviews[0]}
                            alt="Uploaded image"
                            className="w-32 h-32 object-cover"
                          />
                         </div>

                      )}

                      {/* Right column with stats in one line */}
                      <div className="flex-1">
                        <div className="flex flex-col space-y-4">


                         {/* Key information in a single row with three columns */}
<div className="grid grid-cols-3 gap-4 sm:gap-8 w-full">
  <div className="text-center">
    <p className="text-xs sm:text-sm font-medium text-gray-500 dark:text-gray-400">
      Results Found
    </p>
    <p className="text-base sm:text-xl font-bold text-gray-900 dark:text-white">
      {searchResults.length}
    </p>
  </div>
  <div className="text-center">
    <p className="text-xs sm:text-sm font-medium text-gray-500 dark:text-gray-400">
      Faces Searched
    </p>
    <p className="text-base sm:text-xl font-bold text-gray-900 dark:text-white">
      {/* Display faces count or loading/timeout state here as well */}

      {facesCount !== undefined ? (
        <>{facesCount.toLocaleString()} <span className="text-white/90">Faces</span></>
      ) : facesCountStatus === 'loading' ? (
        <>
          <span className="inline-block animate-pulse">
            Loading<span className="animate-[blink_1s_infinite]">...</span>
          </span>
        </>
      ) : (
        <>200M+ <span className="text-white/90">Faces</span></>
      )}

    </p>
  </div>
  <div className="text-center">
    <p className="text-xs sm:text-sm font-medium text-gray-500 dark:text-gray-400">
      Report Created
    </p>
    <p className="text-base sm:text-xl font-bold text-gray-900 dark:text-white">
      {new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
    </p>
  </div>
</div>
                          {/* Certainty indicators in a single row with four columns on desktop, stacked on mobile */}
                          <div className="grid grid-cols-2 sm:grid-cols-4 gap-2 sm:gap-4 w-full">
                            {/* Fixed order from Zero, High, Medium, Low */}
                            {[
                              {
                                type: "Zero Certainty",
                                count: searchResults.filter(r => r.confidence < 70).length,
                                range: "50-69% Match",
                                bg: "bg-red-50 dark:bg-red-900/30",
                                text: "text-red-800 dark:text-red-300",
                                textBold: "text-red-900 dark:text-red-200",
                                emoji: "🔴"
                              },
                              {
                                type: "High Certainty",
                                count: searchResults.filter(r => r.confidence >= 90).length,
                                range: "90-100% Match",
                                bg: "bg-green-50 dark:bg-green-900/30",
                                text: "text-green-800 dark:text-green-300",
                                textBold: "text-green-900 dark:text-green-200",
                                emoji: "🟢"
                              },
                              {
                                type: "Medium Certainty",
                                count: searchResults.filter(r => r.confidence >= 83 && r.confidence < 90).length,
                                range: "83-89% Match",
                                bg: "bg-blue-50 dark:bg-blue-900/30",
                                text: "text-blue-800 dark:text-blue-300",
                                textBold: "text-blue-900 dark:text-blue-200",
                                emoji: "🔵"
                              },
                              {
                                type: "Low Certainty",
                                count: searchResults.filter(r => r.confidence >= 70 && r.confidence < 83).length,
                                range: "70-82% Match",
                                bg: "bg-yellow-50 dark:bg-yellow-900/30",
                                text: "text-yellow-800 dark:text-yellow-300",
                                textBold: "text-yellow-900 dark:text-yellow-200",
                                emoji: "🟡"
                              }
                            ].map((card, index) => (
                              <div
                                key={index}
                                className={`
                                  flex flex-row sm:flex-col items-center p-2 sm:p-4 ${card.bg} rounded-lg
                                  ${card.count === 0 ? 'opacity-70 grayscale' : ''}
                                `}
                              >
                                <span className="text-2xl sm:text-3xl sm:mb-2 mr-2 sm:mr-0">{card.emoji}</span>
                                <div className="text-center">
                                  <h3 className={`text-xs sm:text-sm font-medium ${card.text} sm:mb-1`}>{card.type}</h3>
                                  <p className={`text-lg sm:text-2xl font-bold ${card.textBold}`}>
                                    {card.count}
                                  </p>
                                  <p className={`text-xs font-medium ${card.text} hidden sm:block`}>
                                    {card.range}
                                  </p>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Match Score Explanation */}
                  <div className="px-4 sm:px-6 pb-4 sm:pb-6">
                    <div className="p-3 sm:p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                      <h3 className="font-semibold text-gray-800 dark:text-white mb-2 text-sm sm:text-base">About Match Scores</h3>
                      <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 mb-3">
                        The match quality score ranges from 0 to 100 and indicates how closely the face matches. 100 means the exact same photo was found.
                        A score of 83 could indicate the same person from a different angle in a different environment.
                      </p>
                      <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                        Usually, a score of 83 and higher indicates a reliable match. This is only true if you upload a quality photo that shows an unobstructed face.
                      </p>
                    </div>
                  </div>

                  {/* Face Match Report section */}
                  <div className="p-6">
                    <div className="flex items-center justify-between mb-6">
                      <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                        Match Quality Scores
                      </h2>

                      {/* Toggle navbar visibility */}
                      <button
                        onClick={() => setShowNavbar(!showNavbar)}
                        className="flex items-center text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                      >
                        {showNavbar ? (
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
                          </svg>
                        ) : (
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                        )}
                      </button>
                    </div>

                    {/* Domain Source Pills - more touch friendly and responsive */}
                    {showNavbar && (
                      <div className="mb-6 p-3 sm:p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                        <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Top Website Sources</h3>
                        <div className="flex flex-wrap gap-2">
                          <button
                            onClick={() => setSelectedDomains([])}
                            className={`inline-flex items-center px-3 sm:px-4 py-2 rounded-full text-xs sm:text-sm font-medium
                              ${selectedDomains.length === 0
                                ? 'bg-blue-600 text-white'
                                : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600'
                              } transition-colors min-h-[36px]`}
                          >
                            🌐 All Sources ({searchResults.length})
                          </button>

                          {/* Show only top 5 domains by highest score */}
                          {getDomainStats().slice(0, 5).map(domain => (
                            <button
                              key={domain.domain}
                              onClick={() => {
                                // Toggle domain selection
                                if (selectedDomains.includes(domain.domain)) {
                                  setSelectedDomains(selectedDomains.filter(d => d !== domain.domain));
                                } else {
                                  setSelectedDomains([...selectedDomains, domain.domain]);
                                }
                              }}
                              className={`inline-flex items-center px-3 sm:px-4 py-2 rounded-full text-xs sm:text-sm font-medium
                                ${selectedDomains.includes(domain.domain)
                                  ? 'bg-blue-600 text-white'
                                  : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600'
                                } transition-colors min-h-[36px]`}
                            >
                              {domain.emoji} {domain.domain} ({domain.count})
                            </button>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Toast notification for loading more results */}
                    <AnimatePresence>
                      {isToastVisible && (
                        <motion.div
                          initial={{ opacity: 0, y: 50 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: 50 }}
                          className="fixed bottom-5 left-1/2 transform -translate-x-1/2 bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg z-50 flex items-center"
                        >
                          <svg className="animate-spin h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Loading more results...
                        </motion.div>
                      )}
                    </AnimatePresence>

                    {/* Results grid with pagination */}
                    <div ref={resultsContainerRef} className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                      {/* Filter results by selected domains */}
                      {(() => {
                        const filteredResults = selectedDomains.length === 0
                          ? searchResults
                          : searchResults.filter(r => selectedDomains.includes(extractDomain(r.sourceUrl)));

                        return (
                          <>
                            {filteredResults
                              .slice(0, visibleResults)
                              .map((result, index) => (
                                <div key={result.id} className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                                  {/* Insert halfway marker after half of visible results */}
                                  {index === Math.floor(visibleResults / 2) && (
                                    <div ref={halfwayRef} className="absolute opacity-0 pointer-events-none" />
                                  )}

                                  <div className="relative aspect-video bg-gray-100 dark:bg-gray-900">
                                    <Image
                                      src={result.thumbnail || '/placeholder-image.jpg'}
                                      alt={result.title || extractDomain(result.sourceUrl)}
                                      fill
                                      style={{ objectFit: 'cover' }}
                                      className="rounded-t-lg"
                                    />

                                    {/* Confidence score badge - cleaner style with color coding */}
                                    <div className={`absolute top-2 right-2 px-2 py-1 rounded-md text-white text-xs font-bold flex items-center ${
                                      result.confidence >= 90 ? 'bg-green-500' :
                                      result.confidence >= 83 ? 'bg-blue-500' :
                                      result.confidence >= 70 ? 'bg-yellow-500' :
                                      'bg-red-500'
                                    }`}>
                                      <svg className="w-3 h-3 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                                      </svg>
                                      {result.confidence.toFixed(1)}%
                                    </div>

                                    {/* Corrected template literal for bottom border class */}
                                    <div className={`absolute bottom-0 left-0 right-0 h-1 ${
                                      result.confidence >= 90 ? 'bg-green-500' :
                                      result.confidence >= 83 ? 'bg-gradient-to-r from-green-400 to-blue-400' :
                                      result.confidence >= 70 ? 'bg-gradient-to-r from-blue-400 to-yellow-400' :
                                      'bg-gradient-to-r from-yellow-400 to-red-400'
                                    }`}></div>

                                    {/* Add warning icons based on score */}
                                    {result.confidence < 70 && (
                                      <div className="absolute top-2 left-2 bg-red-500 text-white rounded-full p-1">
                                        <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                        </svg>
                                      </div>
                                    )}
                                    {result.confidence >= 70 && result.confidence < 83 && (
                                      <div className="absolute top-2 left-2 bg-yellow-500 text-white rounded-full p-1">
                                        <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                        </svg>
                                      </div>
                                    )}

                                    {/* Display domain badge */}
                                    <div className="absolute bottom-2 left-2 bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded-full flex items-center">
                                      <span className="mr-1">{getPlatformEmoji(extractDomain(result.sourceUrl))}</span>
                                      <span>{extractDomain(result.sourceUrl)}</span>
                                    </div>
                                  </div>

                                  <div className="p-4">
                                    <div className="flex justify-between items-start mb-3">
                                      <h3 className="text-base font-semibold text-gray-900 dark:text-white truncate">
                                        {result.title || extractDomain(result.sourceUrl)}
                                      </h3>
                                    </div>

                                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 break-words">
                                      <a
                                        href={result.sourceUrl}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="text-[#2964DD] hover:underline"
                                      >
                                        {result.sourceUrl.length > 40 ? result.sourceUrl.substring(0, 40) + '...' : result.sourceUrl}
                                      </a>
                                    </p>

                                    <div className="mt-3 flex justify-end">
                                      <button
                                        className="inline-flex items-center px-3 py-2 rounded border border-gray-300 bg-white text-gray-700 text-sm font-medium shadow-sm hover:bg-gray-50 focus:outline-none dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600 min-h-[40px]"
                                        onClick={() => window.open(result.sourceUrl, '_blank')}
                                      >
                                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                        </svg>
                                        Visit Source
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              ))}
                          </>
                        );
                      })()}
                    </div>

                    {/* Sticky navigation bar */}
                    {showStickyNav && (
                      <div className="fixed bottom-0 left-0 right-0 bg-white/95 dark:bg-gray-800/95 backdrop-blur-md border-t border-gray-200 dark:border-gray-700 shadow-lg py-3 px-6 z-50 transition-all">
                        <div className="container mx-auto flex items-center justify-between">
                          <div className="flex items-center">
                            <div className="relative h-8 w-8 mr-2">
                              <svg className="w-full h-full" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <polygon
                                  points="50,10 25,25 15,50 25,75 50,90 75,75 85,50 75,25"
                                  stroke="#92A5FF"
                                  strokeWidth="4"
                                  fill="none"
                                  strokeLinejoin="round"
                                />
                              </svg>
                            </div>
                            <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
                              Results: {visibleResults < (selectedDomains.length === 0 ?
                                searchResults.length :
                                searchResults.filter(r => selectedDomains.includes(extractDomain(r.sourceUrl))).length)
                                ? visibleResults : (selectedDomains.length === 0 ?
                                  searchResults.length :
                                  searchResults.filter(r => selectedDomains.includes(extractDomain(r.sourceUrl))).length)} of {selectedDomains.length === 0 ?
                                searchResults.length :
                                searchResults.filter(r => selectedDomains.includes(extractDomain(r.sourceUrl))).length}
                            </div>
                          </div>

                          <div className="flex items-center gap-3">
                            <button
                              onClick={() => {
                                window.scrollTo({
                                  top: 0,
                                  behavior: 'smooth'
                                });
                              }}
                              className="flex items-center px-3 py-1.5 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 text-sm transition-colors"
                            >
                              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                              </svg>
                              Scroll Top
                            </button>

                            <select
                              className="px-3 py-1.5 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 text-sm border-none focus:ring-2 focus:ring-blue-500"
                              onChange={(e) => {
                                if (e.target.value === 'all') {
                                  if (selectedDomains.length > 0) {
                                    setVisibleResults(searchResults.filter(r => selectedDomains.includes(extractDomain(r.sourceUrl))).length);
                                  } else {
                                    setVisibleResults(searchResults.length);
                                  }
                                } else {
                                  setVisibleResults(parseInt(e.target.value));
                                }
                              }}
                              value={visibleResults >= (selectedDomains.length === 0 ?
                                searchResults.length :
                                searchResults.filter(r => selectedDomains.includes(extractDomain(r.sourceUrl))).length) ? 'all' : visibleResults.toString()}
                            >
                              <option value="20">Show 20</option>
                              <option value="50">Show 50</option>
                              <option value="100">Show 100</option>
                              <option value="all">Show All</option>
                            </select>

                            <button
                              onClick={() => {
                                // Use filtered results if a domain filter is active
                                const resultsToExport = selectedDomains.length > 0 ?
                                  searchResults.filter(r => selectedDomains.includes(extractDomain(r.sourceUrl))) :
                                  searchResults;

                                // Format for the Python script
                                const formattedContent = resultsToExport
                                  .map(result => `[${result.confidence.toFixed(1)}] - ${result.sourceUrl}`)
                                  .join('\n');

                                const blob = new Blob([formattedContent], { type: 'text/plain;charset=utf-8' });
                                const url = URL.createObjectURL(blob);
                                const link = document.createElement('a');
                                link.setAttribute('href', url);
                                link.setAttribute('download', `extracted_urls_${new Date().toISOString().split('T')[0]}.txt`);
                                document.body.appendChild(link);
                                link.click();
                                document.body.removeChild(link);
                                URL.revokeObjectURL(url);
                              }}
                              className="flex items-center px-3 py-1.5 rounded bg-blue-600 hover:bg-blue-700 text-white text-sm transition-colors"
                            >
                              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                              </svg>
                              Quick Export All
                            </button>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </main>

      {/* Footer with smooth transition */}
      <div className="transition-opacity duration-500 ease-in-out"
           style={{
             opacity: searchStep === 'upload' && !backgroundFocus ? 1 : 0,
             pointerEvents: searchStep === 'upload' && !backgroundFocus ? 'auto' : 'none'
           }}>
        {searchStep === 'upload' && <Footer />}
      </div>
      <style jsx global>{`
        /* Social icon animations */
        .social-icon-expanded {
          width: 120px;
          transition: width 0.4s ease;
        }

        [data-social-icon] {
          transition: width 0.4s ease;
        }
        
        /* Center text in expanded pills */
        .social-icon-expanded .flex.items-center {
          justify-content: center;
        }
        
        /* Ensure container allows pills to push others */
        .social-pills-wrapper {
          width: max-content;
        }
      `}</style>
    </div>
  );
}
