import { clerkMiddleware, createRouteMatcher } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { checkRateLimit, getClientIP } from "./lib/captcha";

// Check if authentication is disabled via environment variable
const isAuthDisabled = process.env.DISABLE_AUTH === 'true';

// Define public routes that don't require authentication
const isPublicRoute = createRouteMatcher([
  // Allow search page as public for guest usage
  '/',
  '/search',
  '/r/:path*', // Report viewing routes
  // Static pages
  '/about',
  '/contact',
  '/legal',
  '/privacy-policy',
  '/terms-of-use',
  '/faq',
  '/dmca-takedown',
  '/opt-out',
  '/refund-policy',
  '/report-bug',
  '/sar-form',
  '/support',
  // API routes that handle their own auth
  '/api/webhooks/:path*', // Webhook endpoints
  '/api/start-search',    // Search start endpoint
  '/api/search',          // Search progress/results endpoint
  // Static assets and resources
  '/_next/static/:path*',
  '/images/:path*',
  '/api/health', // Health check endpoint
]);

// Security middleware for rate limiting and validation
function securityMiddleware(req: NextRequest): NextResponse | null {
  const { pathname } = req.nextUrl;

  // Apply rate limiting to sensitive endpoints
  if (pathname.startsWith('/api/start-search') ||
      pathname.startsWith('/api/search') ||
      pathname.startsWith('/api/contact')) {

    const clientIP = getClientIP(req) || 'unknown';

    // Different rate limits for different endpoints
    let maxRequests = 10;
    let windowMs = 15 * 60 * 1000; // 15 minutes

    if (pathname.startsWith('/api/start-search')) {
      maxRequests = 5; // Stricter limit for search initiation
      windowMs = 15 * 60 * 1000; // 15 minutes
    } else if (pathname.startsWith('/api/contact')) {
      maxRequests = 3; // Very strict for contact form
      windowMs = 60 * 60 * 1000; // 1 hour
    }

    if (!checkRateLimit(`${clientIP}:${pathname}`, maxRequests, windowMs)) {
      console.warn(`[middleware] Rate limit exceeded for ${clientIP} on ${pathname}`);
      return NextResponse.json(
        {
          error: 'Too many requests. Please try again later.',
          retryAfter: Math.ceil(windowMs / 1000)
        },
        {
          status: 429,
          headers: {
            'Retry-After': Math.ceil(windowMs / 1000).toString(),
            'X-RateLimit-Limit': maxRequests.toString(),
            'X-RateLimit-Remaining': '0',
            'X-RateLimit-Reset': new Date(Date.now() + windowMs).toISOString()
          }
        }
      );
    }
  }

  // Basic security headers and validation
  const userAgent = req.headers.get('user-agent') || '';

  // Block obvious bots and suspicious user agents
  const suspiciousPatterns = [
    /bot/i,
    /crawler/i,
    /spider/i,
    /scraper/i,
    /curl/i,
    /wget/i,
    /python/i,
    /requests/i
  ];

  // Only block for sensitive endpoints, not for legitimate crawlers on public pages
  if (pathname.startsWith('/api/start-search') &&
      suspiciousPatterns.some(pattern => pattern.test(userAgent))) {
    console.warn(`[middleware] Suspicious user agent blocked: ${userAgent}`);
    return NextResponse.json(
      { error: 'Access denied' },
      { status: 403 }
    );
  }

  return null; // Continue processing
}

// Enhanced clerk middleware with custom logic
export default clerkMiddleware((auth, req) => {
  // Apply security middleware first
  const securityResponse = securityMiddleware(req);
  if (securityResponse) {
    return securityResponse;
  }

  // TEMPORARY DISABLE: If auth is disabled, allow all routes
  if (isAuthDisabled) {
    console.log('[middleware] Authentication disabled - allowing all routes');
    return NextResponse.next();
  }

  // Check if this is a public route
  if (isPublicRoute(req)) {
    return NextResponse.next();
  }

  // Let Clerk handle the auth for all routes
  // No need to manually check auth status here as Clerk will do this
  return NextResponse.next();
});

// This configuration ensures the middleware runs on the specified paths
// The negative lookahead pattern excludes certain paths from the middleware
export const config = {
  matcher: [
    // Skip Next.js internals, Clerk auth pages, and static files
    '/((?!_next|_clerk|clerk-proxy|sign-in|sign-up|images|favicon|api/webhooks).*)',
    // Always run middleware for API routes except webhooks
    '/(api(?!/webhooks))(.*)',
  ],
};
